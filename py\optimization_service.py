# -*- coding: utf-8 -*-
# 智能烧结配料优化服务
# 文件名: optimization_service.py 

import sys
import os
import locale

# 设置环境编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese (Simplified)_China.utf8')
    except:
        pass  # 忽略locale设置错误

# 导入 Flask 和其他必要的库
from flask import Flask, jsonify, request
try:
    from flask_cors import CORS
    CORS_AVAILABLE = True
except ImportError:
    CORS_AVAILABLE = False
    print("警告: flask_cors未安装，将使用手动CORS设置")

import numpy as np
from scipy.optimize import minimize
import datetime
import time
from functools import wraps

# 创建 Flask 应用实例
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # 用于session管理

# 启用CORS支持，允许Spring Boot调用
if CORS_AVAILABLE:
    CORS(app, origins=['http://localhost:8080', 'http://localhost:3000', 'http://127.0.0.1:3000'],
         supports_credentials=True)
else:
    # 手动设置CORS
    @app.after_request
    def after_request(response):
        origin = request.headers.get('Origin')
        allowed_origins = ['http://localhost:8080', 'http://localhost:3000', 'http://127.0.0.1:3000']
        if origin in allowed_origins:
            response.headers.add('Access-Control-Allow-Origin', origin)
            response.headers.add('Access-Control-Allow-Credentials', 'true')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Accept,Origin')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

# 创建简单的缓存实现
class SimpleCache:
    def __init__(self):
        self._cache = {}
        self._timestamps = {}

    def get(self, key):
        if key in self._cache:
            if time.time() - self._timestamps[key] < 300:  # 5分钟超时
                return self._cache[key]
            else:
                del self._cache[key]
                del self._timestamps[key]
        return None

    def set(self, key, value, timeout=300):
        self._cache[key] = value
        self._timestamps[key] = time.time()

    def delete(self, key):
        if key in self._cache:
            del self._cache[key]
            del self._timestamps[key]

    def clear(self):
        self._cache.clear()
        self._timestamps.clear()

cache = SimpleCache()

# 性能监控
class PerformanceMonitor:
    def __init__(self):
        self.request_times = []
        self.request_count = 0
        self.error_count = 0

    def record_request(self, duration):
        self.request_times.append(duration)
        self.request_count += 1
        # 只保留最近1000个请求的记录
        if len(self.request_times) > 1000:
            self.request_times.pop(0)

    def record_error(self):
        self.error_count += 1

    def get_stats(self):
        if not self.request_times:
            return {'avg_time': 0, 'request_count': 0, 'error_count': 0}
        return {
            'avg_time': sum(self.request_times) / len(self.request_times),
            'request_count': self.request_count,
            'error_count': self.error_count,
            'success_rate': (self.request_count - self.error_count) / self.request_count * 100 if self.request_count > 0 else 0
        }

perf_monitor = PerformanceMonitor()

# 性能监控装饰器
def monitor_performance(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        try:
            result = f(*args, **kwargs)
            duration = time.time() - start_time
            perf_monitor.record_request(duration)
            return result
        except Exception as e:
            perf_monitor.record_error()
            raise e
    return decorated_function

# JSON序列化辅助函数
def convert_numpy_types(obj):
    """
    将NumPy类型转换为Python原生类型，以便JSON序列化
    """
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.bool_, bool)):
        return bool(obj)
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    return obj

# ======================== 基础数据定义 ========================
# 物料名称列表 - 所有可选用的原料
material_names = [
    "碱性精粉", "酸性精粉", "海瑞", "印粉海娜", "巴西粗粉",
    "俄罗斯精粉", "高炉返矿", "回收料", "钢渣", "氧化铁皮",
    "生石灰", "轻烧白云石", "焦粉", "澳粉纵横"
]

# 人工初选物料配置 (True: 参与计算, False: 不参与计算)
manual_selection = [
    False,   # 碱性精粉（不使用）
    False,   # 酸性精粉（不使用）
    False,   # 海瑞（不使用）
    True,    # 印粉海娜
    False,   # 巴西粗粉（不使用）
    True,    # 俄罗斯精粉
    True,    # 高炉返矿
    True,    # 回收料
    True,    # 钢渣
    False,   # 氧化铁皮（不使用）
    True,    # 生石灰
    True,    # 轻烧白云石
    True,    # 焦粉
    True     # 澳粉纵横
]

# 原料化学成分数据矩阵
# 列: [TFe, CaO, SiO2, MgO, Al2O3, H2O, Ig, Price]
materials = np.array([
    # TFe,   CaO,   SiO2,  MgO,  Al2O3, H2O,   Ig,    Price
    [63.76, 1.94,  4.95,  1.85, 0.60,  8.20,  1.23,  752.21], # 碱性精粉
    [64.89, 0.70,  6.32,  0.92, 0.72,  9.90, -0.05,  752.21], # 酸性精粉
    [58.07, 0.10,  6.21,  0.28, 2.52,  6.00,  9.07,  822.98], # 海瑞
    [63.66, 0.10,  4.01,  0.24, 2.42,  6.70,  1.60,  832.98], # 印粉海娜
    [64.64, 0.20,  4.69,  0.11, 0.73,  6.70,  1.33, 1473.05], # 巴西粗粉
    [62.95, 1.71,  4.61,  3.70, 2.29, 10.00, -0.35,  772.21], # 俄罗斯精粉
    [55.54, 10.60, 5.59,  2.34, 2.09,  0.50,  1.73,  550.00], # 高炉返矿
    [56.16, 6.56,  6.31,  2.39, 2.51, 10.73,  1.74,  100.00], # 回收料
    [26.46, 28.15, 15.43, 2.79, 2.53,  7.60, 12.05,  550.00], # 钢渣
    [69.73, 0.50,  1.50,  0.00, 2.88,  5.90, -1.52,  750.00], # 氧化铁皮
    [0.00,  71.74, 3.52,  2.28, 1.19,  7.00, 16.33,  219.00], # 生石灰
    [0.00,  42.67, 5.31, 26.12, 0.10,  1.50, 19.73,  183.76], # 轻烧白云石
    [0.19,  0.37,  8.82,  0.22, 3.31, 13.15, 79.40,  520.00], # 焦粉
    [60.80, 0.10,  4.35,  0.20, 2.30,  8.30,  6.89,  832.98]  # 澳粉纵横
])

# 目标值设定
targets = {
    'TFe': 55.0,    # 总铁含量目标值
    'R': 1.90,      # 碱度目标值
    'MgO': 2.39,    # 氧化镁含量目标值
    'Al2O3': 1.89   # 氧化铝含量目标值
}

# 约束范围设定
ranges = {
    'TFe': [53.5, 56.5],    # TFe含量范围
    'R': [1.75, 2.05],      # 碱度范围
    'MgO': [1.8, 3.0],      # MgO含量范围
    'Al2O3': [1.5, 2.5],    # Al2O3含量范围
    'Cost': [600, 665]      # 成本范围(元/吨)
}

# 优化目标权重设置
weights = {
    'TFe': 0.5,     # TFe指标权重
    'R': 0.3,       # 碱度指标权重
    'MgO': 0.1,     # MgO指标权重
    'Al2O3': 0.1    # Al2O3指标权重
}

# 优化过程历史记录
objective_history = []

# 湿配比最小阈值设置
MIN_WET_RATIO_THRESHOLD = 2.0  # 低于此值的配比将被强制为0

# ======================== 核心计算函数 ========================
def _calculate_sinter_properties(x):
    """
    计算给定配比下的烧结矿性质

    Args:
        x: 配比向量 (湿基%)

    Returns:
        tuple: (TFe, R, MgO, Al2O3, Cost) 如果计算成功
        None: 如果计算失败
    """
    # 将百分比转换为小数
    wet_ratios = np.array(x) / 100.0

    # 提取各项化学成分
    chem_props = materials[:, :5]  # TFe, CaO, SiO2, MgO, Al2O3
    h2o = materials[:, 5] / 100.0  # 水分含量
    ig = materials[:, 6] / 100.0   # 烧损
    prices = materials[:, 7]        # 价格

    # 计算干基质量
    dry_mass = wet_ratios * (1 - h2o)

    # 计算烧结后质量
    burnt_mass = dry_mass * (1 - ig)
    total_burnt_mass = np.sum(burnt_mass)

    # 检查总质量是否有效
    if total_burnt_mass < 1e-6:
        return None, None, None, None, None

    # 计算各化学成分质量
    component_mass = dry_mass[:, np.newaxis] * (chem_props / 100.0)
    total_component_mass = np.sum(component_mass, axis=0)

    # 计算最终化学成分百分比
    sinter_comp = (total_component_mass / total_burnt_mass) * 100.0
    sinter_TFe, sinter_CaO, sinter_SiO2, sinter_MgO, sinter_Al2O3 = sinter_comp

    # 计算碱度
    sinter_R = sinter_CaO / sinter_SiO2 if sinter_SiO2 > 1e-6 else 999

    # 计算成本
    total_cost = np.sum(dry_mass * prices) / total_burnt_mass if total_burnt_mass > 0 else 0

    return sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, total_cost

def objective(x):
    """
    优化目标函数

    Args:
        x: 配比向量

    Returns:
        float: 目标函数值(越小越好)
    """
    # 计算烧结矿性质
    res = _calculate_sinter_properties(x)
    if res[0] is None:
        return 1e12  # 返回一个很大的数作为惩罚

    sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, _ = res

    # 计算加权平方误差
    loss = (
        weights['TFe'] * (sinter_TFe - targets['TFe'])**2 +
        weights['R'] * (sinter_R - targets['R'])**2 +
        weights['MgO'] * (sinter_MgO - targets['MgO'])**2 +
        weights['Al2O3'] * (sinter_Al2O3 - targets['Al2O3'])**2
    )

    return loss

def objective_with_history(x):
    """
    带历史记录的目标函数

    Args:
        x: 配比向量

    Returns:
        float: 目标函数值
    """
    obj_val = objective(x)
    objective_history.append(obj_val)
    return obj_val

def constraint_sum(x):
    """
    总和约束函数

    Args:
        x: 配比向量

    Returns:
        float: 约束偏差值
    """
    return np.sum(x) - 100.0

def constraint_factory(name, index, low_or_high):
    """
    约束条件工厂函数

    Args:
        name: 约束名称
        index: 性质索引
        low_or_high: 'low'表示下限约束,'high'表示上限约束

    Returns:
        function: 约束函数
    """
    def constraint(x):
        props = _calculate_sinter_properties(x)
        if props[0] is None:
            return -1
        val = props[index]
        bound = ranges[name][0 if low_or_high == 'low' else 1]
        return val - bound if low_or_high == 'low' else bound - val
    return constraint

# 定义约束条件列表
constraints = [
    {'type': 'eq', 'fun': constraint_sum},  # 配比和为100%
    # TFe约束
    {'type': 'ineq', 'fun': constraint_factory('TFe', 0, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('TFe', 0, 'high')},
    # 碱度约束
    {'type': 'ineq', 'fun': constraint_factory('R', 1, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('R', 1, 'high')},
    # MgO约束
    {'type': 'ineq', 'fun': constraint_factory('MgO', 2, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('MgO', 2, 'high')},
    # Al2O3约束
    {'type': 'ineq', 'fun': constraint_factory('Al2O3', 3, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('Al2O3', 3, 'high')},
    # 成本约束
    {'type': 'ineq', 'fun': constraint_factory('Cost', 4, 'low')},
    {'type': 'ineq', 'fun': constraint_factory('Cost', 4, 'high')}
]

def get_dynamic_bounds_and_initial_values():
    """
    根据物料选择动态生成初始值和边界条件

    Returns:
        tuple: (初始值数组, 边界条件列表)
    """
    # 默认边界条件和初始值配置
    material_configs = [
        # 物料名称: (初始值, 最小边界, 最大边界)
        ('碱性精粉', 0.0, 0, 30),      # 如果选择，给予合理的边界
        ('酸性精粉', 0.0, 0, 30),      # 如果选择，给予合理的边界
        ('海瑞', 0.0, 0, 25),          # 如果选择，给予合理的边界
        ('印粉海娜', 21.0, 15, 25),    # 主要精粉
        ('巴西粗粉', 0.0, 0, 20),      # 如果选择，给予合理的边界
        ('俄罗斯精粉', 20.0, 15, 25),  # 主要精粉
        ('高炉返矿', 25.0, 20, 30),    # 返矿
        ('回收料', 7.0, 5, 10),        # 回收料
        ('钢渣', 4.0, 2, 6),           # 渣料
        ('氧化铁皮', 0.0, 0, 8),       # 如果选择，给予合理的边界
        ('生石灰', 6.0, 5, 8),         # 添加剂
        ('轻烧白云石', 4.0, 2, 5),     # 添加剂
        ('焦粉', 4.0, 3, 5),           # 燃料
        ('澳粉纵横', 9.0, 8, 15)       # 精粉
    ]

    x0 = []
    bounds_list = []

    for i, (name, init_val, min_bound, max_bound) in enumerate(material_configs):
        if manual_selection[i]:  # 如果物料被选择
            x0.append(init_val)
            bounds_list.append((min_bound, max_bound))
        else:  # 如果物料未被选择
            x0.append(0.0)
            bounds_list.append((0, 0))

    return np.array(x0), bounds_list

def run_optimization():
    """
    运行优化算法

    Returns:
        tuple: (最优配比向量, 优化结果对象, 初始配比向量, 算法详细信息)
    """
    print("\n" + "="*60)
    print("🔥 智能烧结配料优化算法启动")
    print("="*60)

    # 动态设置初始值和边界条件
    x0, bounds_list = get_dynamic_bounds_and_initial_values()

    # 显示选择的物料信息
    selected_materials = [material_names[i] for i, selected in enumerate(manual_selection) if selected]
    print(f"📋 选择的物料数量: {len(selected_materials)}")
    print(f"📋 选择的物料: {', '.join(selected_materials)}")

    # 根据人工选择调整边界条件
    current_bounds = list(bounds_list)
    current_x0 = x0.copy()
    unselected_indices = [i for i, selected in enumerate(manual_selection) if not selected]

    for i in unselected_indices:
        current_bounds[i] = (0, 0)
        current_x0[i] = 0.0

    # 归一化初始值 - 只对选择的物料进行归一化
    selected_indices = [i for i, selected in enumerate(manual_selection) if selected]
    if selected_indices:
        # 计算选择物料的总和
        selected_sum = sum(current_x0[i] for i in selected_indices)
        if selected_sum > 1e-6:
            # 按比例调整选择的物料，使总和为100
            for i in selected_indices:
                current_x0[i] = current_x0[i] / selected_sum * 100.0
        else:
            # 如果所有选择的物料初始值都为0，平均分配
            avg_value = 100.0 / len(selected_indices)
            for i in selected_indices:
                current_x0[i] = avg_value

    print(f"🎯 目标化学成分: TFe={targets['TFe']:.2f}%, R={targets['R']:.2f}, MgO={targets['MgO']:.2f}%, Al2O3={targets['Al2O3']:.2f}%")
    print(f"⚖️ 权重设置: TFe={weights['TFe']}, R={weights['R']}, MgO={weights['MgO']}, Al2O3={weights['Al2O3']}")

    # 清空优化历史
    objective_history.clear()
    best_solution = None
    algorithm_details = {
        'iterations': 0,
        'convergence_history': [],
        'selected_materials_count': len(selected_materials),
        'selected_materials': selected_materials,
        'initial_objective': None,
        'final_objective': None,
        'optimization_method': 'SLSQP',
        'constraints_satisfied': False
    }

    print("\n🔄 开始SQP算法优化...")
    print("-" * 40)

    # 迭代优化
    for i in range(10):
        try:
            print(f"📊 第 {i+1} 轮优化迭代...")

            solution = minimize(
                objective_with_history,
                current_x0,
                method='SLSQP',
                bounds=current_bounds,
                constraints=constraints,
                options={'maxiter': 500, 'ftol': 1e-7, 'disp': False}
            )

            algorithm_details['iterations'] = i + 1

            if solution.success:
                best_solution = solution
                x_opt = best_solution.x

                # 记录收敛历史
                current_obj = objective(x_opt)
                algorithm_details['convergence_history'].append({
                    'iteration': i + 1,
                    'objective_value': float(current_obj),
                    'constraint_violation': abs(np.sum(x_opt) - 100.0)
                })

                print(f"✅ 第 {i+1} 轮优化成功，目标函数值: {current_obj:.6f}")

                # 检查是否有配比低于阈值
                below_threshold = [j for j, r in enumerate(x_opt)
                                 if 0 < r < MIN_WET_RATIO_THRESHOLD
                                 and current_bounds[j] != (0,0)]

                if not below_threshold:
                    algorithm_details['final_objective'] = float(current_obj)
                    algorithm_details['constraints_satisfied'] = True
                    print(f"🎉 优化收敛成功！最终目标函数值: {current_obj:.6f}")
                    return x_opt, best_solution, x0, algorithm_details

                # 将低于阈值的配比设为0
                print(f"⚠️ 发现 {len(below_threshold)} 个物料配比低于阈值，进行调整...")
                for idx in below_threshold:
                    current_bounds[idx] = (0, 0)
                    print(f"   - {material_names[idx]}: {x_opt[idx]:.3f}% -> 0%")
                current_x0 = x_opt
            else:
                print(f"❌ 第 {i+1} 轮优化失败: {solution.message}")
                if i == 0:
                    # 第一次失败时，添加随机扰动
                    print("🔄 添加随机扰动重试...")
                    current_x0 += np.random.uniform(-1, 1, len(current_x0))
                    for j, (low, high) in enumerate(current_bounds):
                        current_x0[j] = np.clip(current_x0[j], low, high)
                else:
                    break
        except Exception as e:
            print(f"💥 优化迭代 {i+1} 异常: {e}")
            break

    if best_solution:
        algorithm_details['final_objective'] = float(objective(best_solution.x))
        print(f"⚠️ 优化完成但未完全收敛，最终目标函数值: {algorithm_details['final_objective']:.6f}")
    else:
        print("❌ 优化失败，未找到可行解")

    return (best_solution.x, best_solution, x0, algorithm_details) if best_solution else (None, None, x0, algorithm_details)

# ======================== Flask API路由定义 ========================

@app.route('/')
def index():
    """
    主页路由
    """
    return jsonify({
        'message': '智能烧结配料优化服务',
        'version': '1.0.0',
        'status': 'running',
        'endpoints': {
            'optimize': '/api/optimize',
            'health': '/health',
            'info': '/info'
        }
    })

@app.route('/health')
def health_check():
    """
    健康检查接口
    """
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.datetime.now().isoformat(),
        'service': 'Python优化服务'
    })

@app.route('/info')
def service_info():
    """
    服务信息接口
    """
    stats = perf_monitor.get_stats()
    return jsonify({
        'service_name': '智能烧结配料优化服务',
        'version': '1.0.0',
        'python_version': sys.version,
        'performance_stats': stats,
        'material_count': len(material_names),
        'selected_materials': sum(manual_selection),
        'target_values': targets,
        'constraint_ranges': ranges
    })

@app.route('/api/optimize', methods=['POST'])
@monitor_performance
def optimize_recipe():
    """
    配料优化API接口
    接收优化参数，返回最优配料方案
    """
    try:
        # 获取请求数据
        request_data = request.get_json()

        if not request_data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        # 验证必需参数
        required_params = ['targetTfe', 'targetR', 'targetMgO', 'targetAl2O3']
        for param in required_params:
            if param not in request_data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必需参数: {param}'
                }), 400

        # 更新目标值
        global targets, manual_selection, MIN_WET_RATIO_THRESHOLD, ranges
        targets = {
            'TFe': float(request_data['targetTfe']),
            'R': float(request_data['targetR']),
            'MgO': float(request_data['targetMgO']),
            'Al2O3': float(request_data['targetAl2O3'])
        }

        # 更新物料选择（如果前端提供了）
        if 'materialSelection' in request_data:
            material_selection = request_data['materialSelection']
            if len(material_selection) == len(manual_selection):
                manual_selection = material_selection
                print(f"更新物料选择: {sum(manual_selection)}种物料被选择")

        # 更新约束条件（如果前端提供了）
        if 'minIronRatio' in request_data:
            MIN_WET_RATIO_THRESHOLD = float(request_data['minIronRatio'])
            print(f"更新最小配比阈值: {MIN_WET_RATIO_THRESHOLD}%")

        if 'maxCostLimit' in request_data:
            ranges['Cost'][1] = float(request_data['maxCostLimit'])
            print(f"更新最大成本限制: {ranges['Cost'][1]}元/吨")

        print(f"收到优化请求: TFe={targets['TFe']}, R={targets['R']}, MgO={targets['MgO']}, Al2O3={targets['Al2O3']}")

        # 运行优化算法
        x_opt, solution, x0, algorithm_details = run_optimization()

        if x_opt is None:
            return jsonify({
                'success': False,
                'error': '优化算法未能找到可行解，请调整目标参数或约束条件',
                'algorithm_details': algorithm_details
            }), 400

        # 计算最终性质
        final_props = _calculate_sinter_properties(x_opt)
        if final_props[0] is None:
            return jsonify({
                'success': False,
                'error': '优化结果计算失败',
                'algorithm_details': algorithm_details
            }), 500

        # 构建返回结果
        optimal_ratios = {}
        selected_materials = []

        # 输出湿配比详细信息到控制台
        print("\n" + "="*60)
        print("🎯 === 各原料湿配比 (%) ===")
        print("="*60)

        total_ratio = 0.0
        for i, (name, selected) in enumerate(zip(material_names, manual_selection)):
            if selected and x_opt[i] > 0.01:  # 只包含有意义的配比
                optimal_ratios[name] = float(x_opt[i])
                selected_materials.append({
                    'name': name,
                    'ratio': float(x_opt[i]),
                    'selected': True
                })
                total_ratio += x_opt[i]
                print(f"📦 {name:<12}: {x_opt[i]:>6.2f}%")
            elif selected:
                optimal_ratios[name] = 0.0
                selected_materials.append({
                    'name': name,
                    'ratio': 0.0,
                    'selected': False
                })
                print(f"📦 {name:<12}: {0.0:>6.2f}% (未使用)")

        print("-" * 60)
        print(f"📊 总配比: {total_ratio:.2f}%")
        print("=" * 60)

        # 计算性质
        sinter_TFe, sinter_R, sinter_MgO, sinter_Al2O3, total_cost = final_props
        properties = {
            'TFe': float(sinter_TFe),
            'R': float(sinter_R),
            'MgO': float(sinter_MgO),
            'Al2O3': float(sinter_Al2O3),
            'Cost': float(total_cost)
        }

        # 输出化学成分和成本信息
        print("\n🧪 === 烧结矿化学成分 ===")
        print(f"🔸 TFe含量: {sinter_TFe:.2f}%")
        print(f"🔸 碱度R: {sinter_R:.2f}")
        print(f"🔸 MgO含量: {sinter_MgO:.2f}%")
        print(f"🔸 Al2O3含量: {sinter_Al2O3:.2f}%")
        print(f"💰 总成本: {total_cost:.2f}元/吨")
        print("=" * 60)

        # 计算目标偏差
        target_deviations = {
            'TFe_deviation': abs(sinter_TFe - targets['TFe']),
            'R_deviation': abs(sinter_R - targets['R']),
            'MgO_deviation': abs(sinter_MgO - targets['MgO']),
            'Al2O3_deviation': abs(sinter_Al2O3 - targets['Al2O3'])
        }

        # 计算目标达成情况
        target_achievement = {}
        for key in ['TFe', 'R', 'MgO', 'Al2O3']:
            actual = properties[key]
            target = targets[key]
            achievement = max(0, 100 - abs(actual - target) / target * 100)
            target_achievement[key] = float(achievement)

        result = {
            'success': True,
            'optimalRatios': convert_numpy_types(optimal_ratios),
            'properties': convert_numpy_types(properties),
            'targetAchievement': convert_numpy_types(target_achievement),
            'selectedMaterials': convert_numpy_types(selected_materials),
            'optimizationInfo': {
                'iterations': len(objective_history),
                'finalObjectiveValue': float(solution.fun) if solution else None,
                'convergence': bool(solution.success) if solution else False,
                'message': str(solution.message) if solution else 'No solution found'
            },
            'algorithmDetails': convert_numpy_types(algorithm_details),
            'targetDeviations': convert_numpy_types(target_deviations),
            'optimizationSummary': {
                'totalIterations': algorithm_details['iterations'],
                'convergenceAchieved': algorithm_details['constraints_satisfied'],
                'selectedMaterialsCount': algorithm_details['selected_materials_count'],
                'optimizationMethod': algorithm_details['optimization_method'],
                'finalObjectiveValue': algorithm_details.get('final_objective', solution.fun if solution else None)
            },
            'timestamp': datetime.datetime.now().isoformat()
        }

        print(f"✅ 优化完成: 成功={result['success']}, 迭代次数={len(objective_history)}")
        print(f"📊 化学成分偏差: TFe±{target_deviations['TFe_deviation']:.3f}, R±{target_deviations['R_deviation']:.3f}")
        print(f"📊 化学成分偏差: MgO±{target_deviations['MgO_deviation']:.3f}, Al2O3±{target_deviations['Al2O3_deviation']:.3f}")
        print("="*60)

        return jsonify(result)

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': f'参数值错误: {str(e)}'
        }), 400
    except Exception as e:
        print(f'优化过程发生错误: {str(e)}')
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {str(e)}'
        }), 500

@app.route('/api/materials', methods=['GET'])
def get_materials():
    """
    获取物料信息接口
    """
    try:
        materials_info = []
        for i, name in enumerate(material_names):
            material_data = materials[i]
            materials_info.append({
                'name': name,
                'selected': manual_selection[i],
                'properties': {
                    'TFe': float(material_data[0]),
                    'CaO': float(material_data[1]),
                    'SiO2': float(material_data[2]),
                    'MgO': float(material_data[3]),
                    'Al2O3': float(material_data[4]),
                    'H2O': float(material_data[5]),
                    'Ig': float(material_data[6]),
                    'Price': float(material_data[7])
                }
            })

        return jsonify({
            'success': True,
            'materials': materials_info,
            'total_count': len(materials_info),
            'selected_count': sum(manual_selection)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取物料信息失败: {str(e)}'
        }), 500

@app.route('/api/materials/update', methods=['POST'])
def update_material_selection():
    """
    更新物料选择接口
    """
    try:
        request_data = request.get_json()

        if not request_data or 'materialSelection' not in request_data:
            return jsonify({
                'success': False,
                'error': '缺少物料选择数据'
            }), 400

        global manual_selection
        new_selection = request_data['materialSelection']

        if len(new_selection) != len(manual_selection):
            return jsonify({
                'success': False,
                'error': '物料选择数据长度不匹配'
            }), 400

        manual_selection = new_selection
        selected_count = sum(manual_selection)

        print(f"物料选择已更新: {selected_count}种物料被选择")

        return jsonify({
            'success': True,
            'message': f'物料选择已更新，共选择{selected_count}种物料',
            'selected_count': selected_count,
            'total_count': len(manual_selection)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新物料选择失败: {str(e)}'
        }), 500

@app.route('/api/materials/<material_name>/properties', methods=['GET'])
def get_material_properties(material_name):
    """
    获取指定物料的特性信息
    """
    try:
        # 查找物料索引
        material_index = -1
        for i, name in enumerate(material_names):
            if name == material_name:
                material_index = i
                break

        if material_index == -1:
            return jsonify({
                'success': False,
                'error': f'未找到物料: {material_name}'
            }), 404

        # 获取物料数据
        material_data = materials[material_index]

        properties = {
            'name': material_name,
            'selected': manual_selection[material_index],
            'properties': {
                'TFe': float(material_data[0]),
                'CaO': float(material_data[1]),
                'SiO2': float(material_data[2]),
                'MgO': float(material_data[3]),
                'Al2O3': float(material_data[4]),
                'H2O': float(material_data[5]),
                'Ig': float(material_data[6]),
                'Price': float(material_data[7])
            }
        }

        return jsonify({
            'success': True,
            'material': properties
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取物料特性失败: {str(e)}'
        }), 500

@app.route('/api/materials/reset-recommended', methods=['POST'])
def reset_to_recommended():
    """
    重置物料选择为推荐配置
    """
    try:
        global manual_selection

        # 推荐的物料索引（与前端保持一致）
        recommended_indices = [3, 5, 6, 7, 8, 10, 11, 12, 13]

        # 重置选择状态
        manual_selection = [i in recommended_indices for i in range(len(material_names))]
        selected_count = sum(manual_selection)

        print(f"物料选择已重置为推荐配置: {selected_count}种物料被选择")

        return jsonify({
            'success': True,
            'message': f'已重置为推荐配置，共选择{selected_count}种物料',
            'selected_count': selected_count,
            'total_count': len(manual_selection),
            'recommended_materials': [material_names[i] for i in recommended_indices]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'重置物料选择失败: {str(e)}'
        }), 500

@app.route('/api/materials/statistics', methods=['GET'])
def get_material_statistics():
    """
    获取物料统计信息
    """
    try:
        selected_count = sum(manual_selection)
        total_count = len(manual_selection)

        # 按类型统计
        type_stats = {
            'iron_ore': 0,
            'additive': 0,
            'fuel': 0,
            'recycled': 0
        }

        # 物料类型映射（简化版本）
        material_types = [
            'iron_ore', 'iron_ore', 'iron_ore', 'iron_ore', 'iron_ore', 'iron_ore',  # 0-5: 各种精粉
            'recycled', 'recycled',  # 6-7: 返矿、回收料
            'additive',  # 8: 钢渣
            'iron_ore',  # 9: 氧化铁皮
            'additive', 'additive',  # 10-11: 生石灰、轻烧白云石
            'fuel',  # 12: 焦粉
            'iron_ore'  # 13: 澳粉纵横
        ]

        for i, selected in enumerate(manual_selection):
            if selected and i < len(material_types):
                material_type = material_types[i]
                type_stats[material_type] += 1

        # 计算平均价格
        selected_materials_data = [materials[i] for i, selected in enumerate(manual_selection) if selected]
        avg_price = 0
        if selected_materials_data:
            avg_price = sum(material[7] for material in selected_materials_data) / len(selected_materials_data)

        statistics = {
            'total_materials': total_count,
            'selected_materials': selected_count,
            'unselected_materials': total_count - selected_count,
            'selection_rate': (selected_count / total_count * 100) if total_count > 0 else 0,
            'type_statistics': type_stats,
            'average_price': float(avg_price),
            'selected_material_names': [material_names[i] for i, selected in enumerate(manual_selection) if selected]
        }

        return jsonify({
            'success': True,
            'statistics': statistics
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取物料统计失败: {str(e)}'
        }), 500

@app.route('/api/constraints/update', methods=['POST'])
def update_constraints():
    """
    更新约束条件接口
    """
    try:
        request_data = request.get_json()

        if not request_data:
            return jsonify({
                'success': False,
                'error': '缺少约束条件数据'
            }), 400

        global MIN_WET_RATIO_THRESHOLD, ranges
        updated_constraints = {}

        if 'minIronRatio' in request_data:
            MIN_WET_RATIO_THRESHOLD = float(request_data['minIronRatio'])
            updated_constraints['minIronRatio'] = MIN_WET_RATIO_THRESHOLD

        if 'maxCostLimit' in request_data:
            ranges['Cost'][1] = float(request_data['maxCostLimit'])
            updated_constraints['maxCostLimit'] = ranges['Cost'][1]

        if 'ranges' in request_data:
            new_ranges = request_data['ranges']
            for key, value in new_ranges.items():
                if key in ranges and isinstance(value, list) and len(value) == 2:
                    ranges[key] = value
                    updated_constraints[key] = value

        print(f"约束条件已更新: {updated_constraints}")

        return jsonify({
            'success': True,
            'message': '约束条件已更新',
            'updated_constraints': updated_constraints,
            'current_ranges': ranges,
            'min_ratio_threshold': MIN_WET_RATIO_THRESHOLD
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新约束条件失败: {str(e)}'
        }), 500

# ======================== 数据管理接口 ========================

@app.route('/api/materials/update', methods=['POST'])
def update_materials_data():
    """
    更新原料数据矩阵接口
    """
    try:
        request_data = request.get_json()

        if not request_data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        global materials, material_names

        # 更新原料数据矩阵
        if 'materials_matrix' in request_data:
            new_matrix = np.array(request_data['materials_matrix'])
            if new_matrix.shape[1] == 8:  # 确保列数正确
                materials = new_matrix
                print(f"原料数据矩阵已更新，形状: {materials.shape}")
            else:
                return jsonify({
                    'success': False,
                    'error': '原料数据矩阵格式错误，应包含8列数据'
                }), 400

        # 更新原料名称
        if 'material_names' in request_data:
            new_names = request_data['material_names']
            if len(new_names) == len(materials):
                material_names = new_names
                print(f"原料名称已更新，数量: {len(material_names)}")
            else:
                return jsonify({
                    'success': False,
                    'error': '原料名称数量与数据矩阵行数不匹配'
                }), 400

        return jsonify({
            'success': True,
            'message': '原料数据更新成功',
            'materials_count': len(materials),
            'updated_time': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新原料数据失败: {str(e)}'
        }), 500

@app.route('/api/targets/update', methods=['POST'])
def update_targets_data():
    """
    更新目标值设定接口
    """
    try:
        request_data = request.get_json()

        if not request_data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        global targets

        # 更新目标值
        if 'TFe' in request_data:
            targets['TFe'] = float(request_data['TFe'])
        if 'R' in request_data:
            targets['R'] = float(request_data['R'])
        if 'MgO' in request_data:
            targets['MgO'] = float(request_data['MgO'])
        if 'Al2O3' in request_data:
            targets['Al2O3'] = float(request_data['Al2O3'])

        print(f"目标值已更新: {targets}")

        return jsonify({
            'success': True,
            'message': '目标值更新成功',
            'targets': targets,
            'updated_time': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新目标值失败: {str(e)}'
        }), 500

@app.route('/api/constraints/update', methods=['POST'])
def update_constraints_data():
    """
    更新约束条件接口
    """
    try:
        request_data = request.get_json()

        if not request_data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        global ranges, MIN_WET_RATIO_THRESHOLD

        # 更新约束范围
        if 'ranges' in request_data:
            new_ranges = request_data['ranges']
            for key, value in new_ranges.items():
                if key in ranges and isinstance(value, list) and len(value) == 2:
                    ranges[key] = value

        # 更新最小配比阈值
        if 'minRatioThreshold' in request_data:
            MIN_WET_RATIO_THRESHOLD = float(request_data['minRatioThreshold'])

        print(f"约束条件已更新: ranges={ranges}, threshold={MIN_WET_RATIO_THRESHOLD}")

        return jsonify({
            'success': True,
            'message': '约束条件更新成功',
            'ranges': ranges,
            'min_ratio_threshold': MIN_WET_RATIO_THRESHOLD,
            'updated_time': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新约束条件失败: {str(e)}'
        }), 500

@app.route('/api/weights/update', methods=['POST'])
def update_weights_data():
    """
    更新权重设置接口
    """
    try:
        request_data = request.get_json()

        if not request_data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        global weights

        # 更新权重设置
        if 'TFe' in request_data:
            weights['TFe'] = float(request_data['TFe'])
        if 'R' in request_data:
            weights['R'] = float(request_data['R'])
        if 'MgO' in request_data:
            weights['MgO'] = float(request_data['MgO'])
        if 'Al2O3' in request_data:
            weights['Al2O3'] = float(request_data['Al2O3'])

        print(f"权重设置已更新: {weights}")

        return jsonify({
            'success': True,
            'message': '权重设置更新成功',
            'weights': weights,
            'updated_time': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'更新权重设置失败: {str(e)}'
        }), 500

@app.route('/api/data/current', methods=['GET'])
def get_current_data():
    """
    获取当前所有数据设定接口
    """
    try:
        return jsonify({
            'success': True,
            'data': {
                'materials': {
                    'matrix': materials.tolist(),
                    'names': material_names,
                    'count': len(materials)
                },
                'targets': targets,
                'ranges': ranges,
                'weights': weights,
                'min_ratio_threshold': MIN_WET_RATIO_THRESHOLD
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取当前数据失败: {str(e)}'
        }), 500

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({
        'success': False,
        'error': '接口不存在',
        'message': '请检查请求URL是否正确'
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        'success': False,
        'error': '请求方法不允许',
        'message': '请检查HTTP请求方法是否正确'
    }), 405

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        'success': False,
        'error': '服务器内部错误',
        'message': '请联系系统管理员'
    }), 500

# ======================== 应用启动 ========================
if __name__ == '__main__':
    print("=" * 60)
    print("智能烧结配料优化服务启动中...")
    print(f"服务地址: http://localhost:5000")
    print(f"API文档: http://localhost:5000/info")
    print(f"健康检查: http://localhost:5000/health")
    print("=" * 60)

    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
