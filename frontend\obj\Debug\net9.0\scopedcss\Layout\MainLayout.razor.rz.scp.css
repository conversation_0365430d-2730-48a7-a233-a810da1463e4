.page[b-k27jblpyjq] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-k27jblpyjq] {
    flex: 1;
}

.sidebar[b-k27jblpyjq] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-k27jblpyjq] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-k27jblpyjq]  a, .top-row[b-k27jblpyjq]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-k27jblpyjq]  a:hover, .top-row[b-k27jblpyjq]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-k27jblpyjq]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-k27jblpyjq] {
        justify-content: space-between;
    }

    .top-row[b-k27jblpyjq]  a, .top-row[b-k27jblpyjq]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-k27jblpyjq] {
        flex-direction: row;
    }

    .sidebar[b-k27jblpyjq] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-k27jblpyjq] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-k27jblpyjq]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-k27jblpyjq], article[b-k27jblpyjq] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
