{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["8p2snB4iCMFgKDO+yoO8aII+5Yll8iAphWmxfesk+jU=", "UH3pvbzarprP7hjEnRY8Jcbl4Q/Cd2Ke5QplJYFVjfU=", "ehbmoIKhO45y4NH83kkooLdD6832FBeTquc+RBqBx1Y=", "ucBMqM+uKZdXUKI+ZyR/+STNOj1Ij7XzWZOGTcH/LuM=", "9E9arGYIA5vKZJbwiuQKAD3ns8SIBLIg10eKDl9IJts=", "qRPkmGn6f7+pXQlQn+y5EM272vmaK2UavF/DQje7Zcg=", "Jy6KUw8BxtK70kPSAw6C4LyFw/VYx4nCcXG8od+yu6M=", "hj/SGKnKHvX2d7i+z9goBNpvK1h3f2eRtFzDZrNklLA=", "OL2uoZx+QjogC1Fk0PltAiSJdMeU9xnDzkV+7G81Oh4=", "3TUTKO7XK6Vsz9NbXKS1+v9f/GOQKVi5bFMZly6TID4=", "jJcfezwd0Alp9GIBZ+byIc2i7JKqTuBGWnAGGIKqFdo=", "ijrFd9RZbaAocKZz2hyTGibba0AKxo7NZ6GiolyJSKo=", "vRqrlASh0YBvPGoELOmXMyF9H4oXZBEiXtIivGYN1V0=", "ZOPoT5qRBsD15SGde0HNLFAgyd2SXjCvNhFXrnf9h/c=", "7hGqBd6OgJ1q8sfy/4QAgQSzyOJ8OzIKI413z4vcI8I=", "GCyFq6URtj82e7QNsqvY6wdaUCZUQjxw6oeTsl8dbnw=", "NDzmywl1hnKth8a99U9MXci3Lnm+lhx/6BjUsTZWz98=", "t83ERksDnrbXE2wVZU+uSzK1i501et63vvYKtyhDmpU=", "JJhzwXsmBOTTvggpTRbnty0TOfS9TPxXfyjlTKdm4mM=", "Q8znW+ovJRW+bd01a3q+v/fUTeAf4qSITrwwrkSWjbk=", "z1hiHYQM6CjBsDqdE2QdiPjGhoJpneMQVqU6WzzAsxg=", "yQRHGFvpCiH06Jzsuc45zKfDtHCiFHtH+Jyz7LF++B8=", "c04hwiUb/qTHCFks73lpD7I6otrg6gfovalxByab6q0=", "E88jNz4GHSJMwrAtkHcX+E5pbVVc9hFfZ56asFX1Bpk=", "9grc/L2P6S+ptwZFETloqKn6AX53j0sSnee29ENKE1Y=", "5nC/h2iHoQanloGhCK1DY/smk2UoodTk5e2Q8ZWQT0E=", "l7mHO5EYMi3QajZs8dcQMAYpRCmVP/09B/ELlfeaguM=", "AMpIXALt1Ty9V7CmnBtwo0aATjEIIslfXdjxjT3zmpE=", "PhR1kPK2UyB6b5jDqiHKWbtnkGe/3ZsHSoPcQrW8OYQ=", "qY5vDTtNQESO7ZOqsTTKGvuOCXnZr7XYEsnGNYTPkzU=", "MNcjPH28xlIPsfbVWHfb+936qDCIhXJlMhpXC5AO2gE=", "VHWeo29WImhqT1O7YwD5cFtlSJtKOKkOtR8R0Mctj6c=", "C6KUhkO14u2U5seE2tMrRHpTagdhpyr3U41a3HDNsTI=", "kM3/wkZQMVdojSA+gC+xlZpKQA+cNJUcazvRZa/3xss=", "B7wThUVnar5boRC4j2Q83cZ2t/x47Cs70uLeWA5r2UU=", "BqmlWznJSFrqchil2CTbU8njT5kDIuynPkNcEWi4pOM=", "aPrC8tqLpbjpYLnNhgoSpBbNgF7z03sJ6pE4abCaQwo=", "dDHpP5KabEI0d9TUlq7GhutTtawCREdmPKIIJnckUcE=", "50vfl7zZ9i6VRPD9Sr6PxjVC8fp8HHnmPLiSCx3UxKQ=", "Zj3GcZVF8K4imqKuDhq6qqEMlQXftS+gx1PsHzPiQVY=", "mg20SsbIRpuNIk64NJ4+okt9jdbyssufSzhb5phxyiY=", "y5fpg8X9wpVJKvyz/NI17FZe/6alP43E6rFFFzE6j8k=", "ER0kZWRS9f0uaeuAaM4W0o+VygHZ/mTZUPivkwj3JnE=", "rM9+pj/9ly3kTSG7IakJrQdPTNC50evHjVLhAru2WGQ=", "fxugI4gnT2HiUWNYEB6scICNQgEYLiDsDTPK8AWYKIY=", "EdAZ1tdPvneZtX3ybL+GB3fzegflTt1YDhxPNiXmzFQ=", "l33/3Tohrpokr0ubBgf0/RRgTqUekDOosNSe/OF6dwM=", "ZYZLR+jCbbBCSgW3z/TncvT5tsvKx4SsuPqQHwLYDtg=", "TnLimktWC3AATHKfcCVQJZj/48m1Ud5tz/MXEnLndLY=", "xtAQh/mBEb9bazMJfUpHZi6oHiHWF2w3MZUf5wmu8xo=", "6vWFOOm4LeELeyi4SBpdQm63PzBkI4Yc+XzsM4ysYbw=", "X8W45xPLGX8lTrgnHG5Z3DoQHpcvhCK+spnvRFHs/7c=", "HFd7pSVlw7v+bCI/sh/mqoaJ1qZfTC+ZzB2yxVNcG/w=", "ZFJk8/cWIQQgv6IAqZjA1HpUruZVDU3kjf3NXuoEegg=", "sfTcpLlCDdcuuKyE3IIAg9AMUMb0XAPx949VjEBTJDg=", "IVrOnfq7ozRlriQX4WaAVJx4H2fkLEUC+HfYlptgg6U=", "XHmy+XJZDAjQts1AkKoOOdFD623eb82JRlUyOgRgwnc=", "lEskc+TiWkl4NZ98h7utQ897HKA3Ppn6HOvVVm1bS3A=", "N7AT/jfEONItIE6jG8fya3WRujsxgZ4L/fGWhUCEbGk=", "h2y9a8q4IhV5NRY/Eu/N/9wFEZkqiagiSPVIV+fhtV4=", "esMiG4ewdzkIGyh1givTyn0VHBKtiq/GYipYQwpg7ug=", "ECd60pX3D8NpyKuCID38GbgMNVmRq81wp4UGdH4Iwvo=", "sUEh9OM1s0ifcnytcPeuwDlzFjvFmX0aOBO5ZCOyG94=", "lz1T/z8zNqc6DDN+DiwVXn4DHxdqs3utFx1P+o0dMM0=", "y8eTJo/qSHMk4OKtPbKSh85bw5MXnbL3KydFMYFDUXI=", "Aj9zMOLUchR/vcVIK34bkDUCTjlE/EDND4CYe1DLatY=", "sqWff2ujZMFgMWVpoM4R720XER6uiiUgT0cwy18hUAk=", "rHgeKeeL8TnvVbrNfZkDr7s3tov4KLBQGbezRncwwXs=", "CzRujEB4N2bFcWS2T47Lxy2wKWTng/IFUnp40+VanK8=", "cBvY5jVzjznw36VP4piynmBpWf11CNMocKKovfdr/pw=", "b3KBJhCZ5QhRSO00A5p7yECYyeE1q1GetvX87uDTZ6A=", "WKuNZ0jOdfAA+5dwDUfUblrPbFgThgpyh+ydpXXsMj4=", "/lCKII9SGjbg9Dtz3mH8FI+g9cPd9E63TbvP/PdIZno=", "GXvHR4UUAe5/qbhnZyQrpWEBgxB8g8s6uZYOF2TQRnE=", "3efzYH5DWan7WBYiJEYafMMc8i33+3GDHSaG2bklN6M=", "1IZ4GmIUtdtJPUgCc+iJ8NaQ7c3HdjyjjLzvLPNx93I=", "XMDUEKlFLqYheXvdkBnUbGgQoGJ+mIcy/965rkTx2dU=", "gGqfgSL81H8g5UwlAzcPS8uePWPLKFJ/cNt2ApXes14=", "osWlpv6hvUY10piZ31CCiqj6TN+iH/mDomHe7GKPtqQ=", "02Mo1xzm2J6d4WqMRR7ctRU3SmXS0rDAPwcYl97cAsw=", "vGT5fPeK3Qh3GWsDhWEq4blEt/hDcGZwS4byb8U2kRQ=", "ncqWXdUg/jL82DjI/hWcuFVM5q7cL6MoUxeaM4j5do0=", "gB8EByNVHX3eQ+vNIX6GGireWhAfO3YRVsaPEG1Rzo8=", "zo5y0YGTBajZiSgsqTmkP4/eGYavT/dlkhjBYhp/kZg=", "5NS4Mh1ZWVECprTTZZY5tFqd8eDvFktUwsSZeinIS0k=", "F9/lpZOvZY1BEt/hK4SEbe+9pvnligrlxBxEHoi/4wg=", "REKrirGMEfjizsUcMslVTpMVRo7KdhFPcoDrLFc2r7A=", "h5Xdm0Khu10MfJQW/5mq0cxpd7NVeQzbBzKNxYRZKZg=", "W57ilChKNzvTHx9x0EsXvsOmVBv7MnnGbv/aGIODpgY=", "KioBYxjHJtGVtDGXrRpw/wUDJT59nrEIivLGdX6474I=", "wt95Bm1PdqKy5iw9rt7Ud2YtHQPkuG5Wa+APHOTdkBs=", "D5zOnz06mKGEF5z2zT4w1GP9JeF5rMX8VWy54/LIwLk=", "d0+j9s9DyrGgWEkXoKK/14H5hR0pjOU9GWlDqDuh0kY=", "s1qwYEoeyU0IofIP0jxXtNYJ9zk9py0U1fjMWYskflA=", "pzTGYD3K3njpYeEAFMgEihumxkNF9oUeoEJUB6VYYiU=", "TJOYcn9MOJCbkoGzq1pnDyaKLynYjHzQwL+maJriaWs=", "8croQdBB+JZ8rbCrv3OtSwz9hJdB9tx0aX6Wa1quC2U=", "lArm3jUVHx4OJwVOkzXIu6nQqKTFQc6Lc2NayWSWXD0=", "OrJOMp8fL9oXtfdcuw6W6XPhC+viybZzU3yoIMryHiM=", "g1Tk/p/ROJxG5Gc4jEiRk1p+yEcGkCOFi0QNwYxs9eE=", "w9pbyjKqljbw6V2OJGtCVE29Df6vN7SONqViwCu4co0=", "urPevuThf/uUR4aQvK97BFbmaFnpjzgrZwJBXRFirPU=", "5D03x0O0uLmehykZgQSuoHcoVveWcGyQ+2jELCrZ6og=", "jn+IVcsEbhPN+9nEyaKUnoNqfOE9SczRfylfPuGndPk=", "FEwEkla5qtrt2SNpfGbK6u6pwvCvDSEkEeYrT56ovqk=", "kPj7XxEs2PWhPOIVNFva7dA1t0OoC72oQ31HN7VMMfk=", "uVyhltGf/v0afaNhf/tUVnPS5H/J30RJZO0BeLEued8=", "vu+hvl10O7SDQKaaY6KC8Vjn9Xazh2LFrX4MnmHU7qw=", "UPIaHZ/5TngxVOfdOxvp7vAoxBkzerhgsrR280KOmmk=", "n7hsAJgNZ+GK/uYT3eE8APSutuARk+UCVIEW1V8Wgzw=", "eGn7Nw8yM/6rhafCKHBOTCvPpAXcwhZ44p+NCTO4xwA=", "p6t2rVb0Z9xV3e5mEbE4kIPEEWrPIuSxG+687re3BDg=", "6K3j1Y6MMuIHYRNe6kN4UxWua8l72fLYngkWOuN4h4s=", "xBZrld6hoyrebXE8H8YC22HBaHZhTXNCJBmynM8M0Gw=", "88o7KBn8Ycq4otHh/BDkxb5KAG8QDlRldDtVffVqjxU=", "a4VTERGU0+muSuO82TZAfxpnFmpt+WUumqLwyaYPi4I=", "OsEtF3g0CsLD1D9og7qxUhD1g4OIc4gepE/blS48VJ4=", "963UhjAxEoWIbiniDyn02gLVtD/FxzzKpRtgZVZCa1w=", "+c0xj8TAvizuJuYsaCm6bMNzP+AR0UqBccdkkVE5W4I=", "MpRz4Acnvuqq0tfUw3YrMY9ykB0Z8DtAt5vebr2YN74=", "hV5VMfXfMdRGxYcNvSG7mW0s0ysVWwu8hCGz/NsXupA=", "p0HKH8LloW0tutDrKzhE641edZ0CzDfqwaLvQCkE4N4=", "4E+53tjND7F0WMsdAj5WT8Tu7gqEW4ACd9u+ao3qUdo=", "IyBLLDBKfNRi7kxGS5O+MWstqUyBW2hBDQzq38bR8iU=", "7OoVWW2VNCtfVrrf205oRtEmDJAJZyD+WPhbXpytmuk=", "0db+9cw+lqE/qEJJa2VRfLWv+tzEWIE6m2AmmgvF0x4=", "r2XMLBvNM02aNqEamul3sgipM1e3MLeqPIr62jkaJuc=", "rnHquxVtJVAevDFGDIDinQ5pYX8uufkC5ty4zVofduU=", "Pet6r+6YTFnqKrANBWe5XERmlxUb1C89eGMIFgkU8g0=", "ATZy48gmsGdEVdmdC/3wjfXi3jGvLQ6jqIo4FeR0Akg=", "QOSpQxb5QZ536206WLwmjKx3VWMDKOVGI4ePs8+ymqU=", "4SKXncGqIxiuuO2WLnZy19af3b1l7UNubJz2GLB2kIk=", "i+0gqrtpiRVnATmM03RPHMhCbUCHNR7fNIP+Yy3l2QA=", "vDwY76ZhPZR5nSRkbnsgKFbbeO80A3YRD7A3N2T3ntY=", "KOLj54K++hblQGNiO4fAh95VpKwFcLD64SyRnlRgyhg=", "crJlXMLtnSTDwR9/ZyQrRc42SinGD2p5bXWinPlX+Gc=", "nX67KLIaHT6gx70cEfv0tdxqd0Jj26e4qOzQie4rYtY=", "Cbsd4HJLvJXITjyd7PfErhwjlX5f7uWFiNgylZZM6Y4=", "SM7X6oYDpFCwubAdUCZjeeE1AFReGwzbbvSsTJCjX0Q=", "36efNjP5tosahgmVIfh9FDnBJHSS10fW6l5g6k6x6FE=", "UhgGgtAOIbpzdOTJjw7rmkNmko1af3SlD0kilzQnUsA=", "KQEQldG82VAO4j+B7hlkQOp7n9Zpd/BlaQ81iY8OrW4=", "+3zWJdtCwDSPan1nvT0m9oUHrNf0fzftG0jdM6o8138=", "zVRBEZl4u7kHSFiJAQHJRE16bBiKBRDP5pO0OeKm7YQ=", "75FKvanIgCXMVB4DR4RC/VvT1lt/cSEYk/bCmCJC7QI=", "8h4HKfHTzKFcI01HQ7E7hnJaxdl/8jtKc+5yn6T21zI=", "99OYFRS00Sm1d/iXLsD/WpIiIUGawp8NEkDHsHFYDrg=", "VG5hiZlZaA+bC3vWdd34Lb4iza/BE/TzNs7fZL3fs4k=", "3MBBgBzbpuHcERak631UppBxMHfYHGQHkjHmPcSol2o=", "IrnapFR6gdrWsDGBqL4Bw9BGhspoahGAS18HmaxFW8M=", "FBR0wq0EDLF2B0otyr1W6LseKtUH9BioIbimmCBU40o=", "6veHCPFHS73lT3oMSM2YPUI8Sg5YXN74j6bZZEDrCeI=", "rVGjfyFbx7OBsWH/1H0EymQBUzLSJaCIdr2iqkX5yLQ=", "ijns7BTg6vLNlNotJQt2qKLS+ksE+cViQryg/gPHIIw=", "7RuUHBAlXWSEP4Ectr8OQRSflOEw0RTuzMWTk9gTHSk=", "PV9Be7CJEbMDtp1zy8td8KSSjzoGbx4gISJIQAu7LsE=", "2lrOcJH7sbkmBdywztHfgtwiRMY1A8CxkBviu5+nMQw=", "52286F4PdTJWluyXjUViIcyvdr/GZ41uJ15EvNx9ZkQ=", "YUwr5nz4ROV5dGFpWHLbcIMqYnAcQ7vYgjFVx6lb3IQ=", "eTUqtyB2RIeGXJyvCzeyX4EfMi/OAIv0Ak7P9Axd6hE=", "UzEkSRLiuZMjlRuQMXoRk2xL7H8VGFLCnplsQaASjn4=", "QMl0yKFeSL/+FI8GDJKZjA7Ar0UtGWFCVJFb/PSiD4o=", "7zeOlp3gDPQGWuy5Jo/HlfmIL79vWntYgfSqHo5iPlc=", "qzCoFn3zfYEnsZ21XsBcx93kFwYKwNCtRHUtkRDSIuE=", "hZloCDhpWa6zzpMzzbIcGoulVLp+AGCI564oqNYZzO4=", "VkFwnbyo8y8MFg1OBROSw7oiMdfql1SovhvjStlXBx4=", "9KhbpMFlAmt8f2lz3yknFBAMGF+2CLqO9NMOnHtg62w=", "RgaX3JUbZIzy5fYC3oYNlHmxTZ9nFeo0BzQvSwpz5n0=", "BWGDSEJouoHrq1lI9wN3M3SW2+xLWeNmxdkoWG0CdyY=", "T+hSknmWBEcBpGoLawuN4id1RPUCIEvmxQ2y6dLOG9M=", "qftHf2vSr4UADe+VxemPv5FKX/OK49MGd0vamUz58zQ=", "5LusV/xtJe2NWyctlDR0kv+UvdOrfjUWSsvD/bUVNSI=", "c9WOfrbtCXkWB2LYec7/zgsVNTKMaQ2r66Xwd0MhB7I=", "vFFWSrCi+9i3D3miGGbWgfd221hO5uxXkOxzXhamf60=", "YtXAdQwgOq9RS9arxt+gEDuSXgrheKKGyuhdVZ8Gl1E=", "WHZpJFEq2dLuUmyONaR74/3HPVutuDUnRMl0IvhRDUY=", "mcRZWP79hTm10sCF7T8owcOc6BtL7zczSBgxZFTVSqg=", "2qTWwjbwRimV1mx7w7C0CQkbgpGqx0hEVcMByjLccqc=", "356Hq+fKGVKvQcvCAXA0P7DaNAAw2qgaFgTNSbU3zmk=", "TYB+VK9U105l/yJCgnWPqo7OuAL1CXpGiwffmyCRqJE=", "XMnsPU3aMNG/wo5vrrVovw8jwD9l/kVw25W1v8qqvno=", "fD0dm/GampxCwUq3RK3OfZYG73DGgyYIHDBzAIWJExY=", "xpFSryNcZ58e4WCedHO9gPJqj9hPxzOx7UPaOE5P/3A=", "7j2HfidQsls6FMYWlrwicpT002A6EMbFiDQNHFoLZEg=", "MUsgtvD6k4DXMmW2sEl7RrEhv4MNaCSVpoFb8iQW6Rc=", "NZiEYMWEDlvl4Dt5lti+hrqX5HbZ4ol1UNvASDIpZPo=", "T2K+K8z4iUYRkoDiMQpcJuC/CMhOOB3qdeKKEvJscVA=", "6a23Kw7nVJ4X5vyzD29GC+m+HosaVWFSWgsGMNt898A=", "QlaOYRagHHbGegxc0FZxcNPEHlu4/Qrf4A7oGEN14CI=", "iOsNfV9HNoPET+W1iIHUBzJBdefK8UrbtHBPRun9Fdw=", "mCtfT5sI9KYxisiUgzWXI9fXWDYe2qgHRT7E+tVn7lo=", "dCiXVa3DGT1DpaFWMqe1zpvpENwXS4x8KRq1eOmI/p0=", "63E9KxHKYtkf5I7J/a7THHg1NlzxLLeSDqMUTz1thdM=", "zGYOn0GYYhtGAfGoFaIbTIYj7Jnkk/HJLOsLRoRBAkc=", "MoSIsD0rCl74XXKynd8szqj26Wnis3O3EqcEaomz21s=", "odywdd0GwxGSoMcXVRGAlPcydibKZl3XzapLB1c253M=", "sxm/GwD4P+tP3kfanpg40PArm9mEaPkolpfXntbfhds=", "1rh4zuMhUB0FwA7pYulV9nI6lbQhd4mV5tmUtKPTky8=", "OAmmPiyxq0igbtqjWgRJTCN5cdWCYppN6ll1p9EDAco=", "w7UCX4ht4CvR1/HfwnFzQgAEL9v+I1sJyw3tyfF+qkw=", "IUEsNb0NG3yp8JrOjkMM2ZBxR9L/cf9KIVpXkrnX/IE=", "v2AC1eXgutQvBPmJs8YW6MABn9/x5j/iK2d1ZFrT0A4=", "AOPTU5MFmMY3uc9EsFBSsIY3qriY4mGVAfMb/kRo/8k=", "jpBFwQrS5HOXzhO7HIsv4VZxjKwhJE/dX3IllGDUslk=", "Uu6wYVpVu/bwtuofYK6gnie96UtApN/T+UAUwMIRCj4=", "AaAIhfQU+P9NF2G5fk91gUfsFECIRvdJSI0gUxlX0f0=", "wTOMpwLDDoT9glYnki7joC7nh7xcbodC2cpUdCat1og=", "9S3yJ9GHtaGMG7BKiP2y+/9ubRQJ2QnJ8oyNkndblJY=", "+EBErKvfxcIfFsl7jsh2P2pbemqJGzZvMNcG5E9ccjM=", "OHsUkU/eYhtKpRcTP6PRLj1i1jAQhY6ruikM/SgBx8s=", "fF1bHjrzvqnZC5sYV0/1uF4BLuNQg7y9DwzbltWsImI=", "9Ni+IT2cDLGDkMWezCiHvK4LN51fXD8rBiecckKrp8o=", "0kESHV5HLn5+4CwARwFuhjmVd4vxVpuEeUvRF7KlVhQ=", "dsu1CkTcDL3glj46vO2yFOqhtFGRMImA651rTxbLltw=", "4NrZi6wsqhZ74m6SrmX0aNLzZ1RhBYk/j/M31ItdBDg=", "gcljIYPTFQaihSFu1hY6UxSJwPCIhJqFO6Q7CzuzgTk=", "9bOVir8EYsxOg7H/kE7zJK9VELBF8CnWwB+033J9NYA=", "rbQDTOnLQxlMmi2DKVDZn+AJ9Knf3K148QrUmDs8PqY=", "u5Lry5svmjZMqgguQdG+n2huDHPtKIN6944UmqXP1vk=", "fr7/O+To83tRTzBHYCr07KJE3PaCoviGTLOuguMJZUA=", "+4kaNkZqRXavHa3KcWCR9fYkgqA7Dz3P/q2jCcfc1uA=", "A5pF9bNFZ9kE4rJ6vbHJivjLVUhkRhCL5eI9PSQ/xX4=", "twvab5dfwryAi71QJ7MCEExgsNv4xBjChLjihRquchg=", "C+UZ78NI0NdkaF5uLrX3i+ic8h5kor5uhDQN3afz8GI=", "rzQ0GP1OGJcbAZkDbTFtNysAD46nbew9R1EmmIrkSPg=", "VAQjCaK7KtR7FT8c20a9x4uXKzaMyc6EbuWpS8StQb4=", "0EaCNo8dH5PxzAdbSdUGu24rNxpVkhQXM4/hwkzQN2Y=", "c9I9OKRvLlvCaCFVKBhOtVW+ZxY+CbQObjVAdXpTYFs=", "XxhgEfFgfFvlZgvb6kQ/IqngemMwR8HVWhCvn1c7JiE=", "eXHOJR/0hoV2RiWxL71I23QNobnRxdrP0WBGiQyvhhs=", "ssL7R3cE9v7bTtBd+ajH4aWuY/nkaLrmbPjP9NVXS5M=", "dFsaoZfzTQ9aCVujb7GCmAOHS4NrTwUccSU6admd2rg=", "xv4/3UHr+4tH7HWAT8vSpAuSrC260tYq+vzdlzqsppE=", "c+lRODl09zZXYwFZQuJ3dfPw9sX9yF5lSoChtFe8w/A=", "vJD8YBiRdolk+dXaTHosMS53uT1cyhkexF474fnl7U0=", "V4sMneHEzjdm4721OcmbeEhXnUeLwDT8IFkuAK99QX8=", "pY0CHWJWXdagrLPi/Q4jjDA1RGx48uzKpkB/kP64e2s=", "pPviJ+mgeQhOi2alhX20mT8G1oVoluS0x2wXuE9ywcU=", "yV3NX4JVadhCoOsG4MbGZ6LnsKOvziafaUV7sD2lDDk=", "hNMwZbtOftX1+PxlpNuCL/aWWkSvU6Q0L4CNhtGkBCA=", "SWDkxjH26xAtAKwhEGg1jycU80+nsMcRue6rrFLI3zI=", "lZ/P3P/GSMTIz6J6MwwpMSOH/Qup48d044xJWhcR3PE=", "TikNx2S9bbDTNN04fYeY0D3aW7F6JGSU36bVVFkh2Ys=", "3royt2sy6TXFXP5jyvCN27/9JOtoe7NQonFIZoSOOB0=", "smbdyfDLZ/M6gI5KzvHcqfkAV/pChadPEPXQ3l9PQTQ=", "0NWAhY9iZCssHAHpL0LimdDCo5w/2vK9IRMlBgeM9ww=", "WKmbK8F/+rovGiw7YzfhlFE/TEYpki8XfxSKpyWpT6w=", "uKnqG6Fb7G0dPVKsHmqPqlnX92LlEsIaTkYirWWDVN4=", "etJ9ng7U6wpUCzHAIz1CxZhlumxHocQObHpREdKUby4=", "Yuae2n9XsoLxK2XeRgN2N/7q/9WrXEEQJRxdUEEmtAc=", "ec1U8oIacOeWfgeZechyqn56Uc7UxIUc7GJmEkSMmjo=", "061EZAlTZel2WH6o3Lx8lqSqSmBr9vGtWhGjIj/wMAA=", "Ia11CUdzmWxjdmoJ+u9Wi8NwAmfZuqnMoS/n3LodQKc=", "mr/sGERTEjeetdnJjrCuSHrAanaCTS61wZGC2Av0qeU=", "JFwX59dlel13A1JM6k7n1TG0vB8Sz3m4yJUE8a0NN5Y=", "UK520VmyojR0ahUVc1cre4pRg5z+r6EOmEh8MTs8RBQ=", "kf2KwUrocV88E9anErSOG4cJwZrRVZF5LI8JMnA3DcE=", "Ew17zF6QE85LkyjI3TclZzukA5f8Tqek6ti3hJMITzY="], "CachedAssets": {"8p2snB4iCMFgKDO+yoO8aII+5Yll8iAphWmxfesk+jU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tzxjg6is5z-vu9q1asft2.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.10.0\\staticwebassets\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "po3t14q561", "Integrity": "Fzd+uKnV9c3Hr1lc0bJedrIlRER7oM5BNhwQwEkffHY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.10.0\\staticwebassets\\MudBlazor.min.css", "FileLength": 65117, "LastWriteTime": "2025-07-30T00:47:58.0953064+00:00"}, "UH3pvbzarprP7hjEnRY8Jcbl4Q/Cd2Ke5QplJYFVjfU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0wz98yz2xy-i93b8idyg2.gz", "SourceId": "MudBlazor", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.10.0\\staticwebassets\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zd3mm8t3y8", "Integrity": "p0A6sPb6hWkt5PncGTPXS8wC04VwOd9nQQG4hMtyfZA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mudblazor\\8.10.0\\staticwebassets\\MudBlazor.min.js", "FileLength": 15387, "LastWriteTime": "2025-07-30T00:47:58.0865082+00:00"}, "ehbmoIKhO45y4NH83kkooLdD6832FBeTquc+RBqBx1Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\59ecn79knu-8rbvw3on5j.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint=8rbvw3on5j}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\css\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ri8oomj080", "Integrity": "3V6n6GUWrXibPBz7fhqi3nKUDtOkIOblRDVtLs9wCh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\css\\app.css", "FileLength": 2031, "LastWriteTime": "2025-07-30T00:47:58.0943316+00:00"}, "ucBMqM+uKZdXUKI+ZyR/+STNOj1Ij7XzWZOGTcH/LuM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6vujlonbcr-f14of2wzb9.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint=f14of2wzb9}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n8kml1ksk3", "Integrity": "9yihtVcBJgIri9izB7oRkDB5455ucPb6Pu3ZpQ1iX4U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\index.html", "FileLength": 477, "LastWriteTime": "2025-07-30T00:47:58.0973014+00:00"}, "9E9arGYIA5vKZJbwiuQKAD3ns8SIBLIg10eKDl9IJts=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zrgqg52ngv-bqjiyaj88i.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-07-30T00:47:58.0992961+00:00"}, "qRPkmGn6f7+pXQlQn+y5EM272vmaK2UavF/DQje7Zcg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gssam1jjr6-c2jlpeoesf.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-07-30T00:47:58.1054337+00:00"}, "Jy6KUw8BxtK70kPSAw6C4LyFw/VYx4nCcXG8od+yu6M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\p45gdd8s7w-erw9l3u2r3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-07-30T00:47:58.1079403+00:00"}, "hj/SGKnKHvX2d7i+z9goBNpvK1h3f2eRtFzDZrNklLA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\dd6j0z6cjr-aexeepp0ev.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-07-30T00:47:58.1131969+00:00"}, "OL2uoZx+QjogC1Fk0PltAiSJdMeU9xnDzkV+7G81Oh4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\s9ueet470w-d7shbmvgxk.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-07-30T00:47:58.0998058+00:00"}, "3TUTKO7XK6Vsz9NbXKS1+v9f/GOQKVi5bFMZly6TID4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\akjbeiunjl-ausgxo2sd3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-07-30T00:47:58.1054337+00:00"}, "jJcfezwd0Alp9GIBZ+byIc2i7JKqTuBGWnAGGIKqFdo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\11tl5xjpn6-k8d9w2qqmf.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-07-30T00:47:58.0998058+00:00"}, "ijrFd9RZbaAocKZz2hyTGibba0AKxo7NZ6GiolyJSKo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\10kwkd4ey1-cosvhxvwiu.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-07-30T00:47:58.1064321+00:00"}, "vRqrlASh0YBvPGoELOmXMyF9H4oXZBEiXtIivGYN1V0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\72f3nk5sw5-ub07r2b239.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-07-30T00:47:58.114215+00:00"}, "ZOPoT5qRBsD15SGde0HNLFAgyd2SXjCvNhFXrnf9h/c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3jmlvd4j4c-fvhpjtyr6v.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-07-30T00:47:58.1241536+00:00"}, "7hGqBd6OgJ1q8sfy/4QAgQSzyOJ8OzIKI413z4vcI8I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\9cpaah32pt-b7pk76d08c.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-07-30T00:47:58.1079403+00:00"}, "GCyFq6URtj82e7QNsqvY6wdaUCZUQjxw6oeTsl8dbnw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3dq30cxno8-fsbi9cje9m.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-07-30T00:47:58.1109279+00:00"}, "NDzmywl1hnKth8a99U9MXci3Lnm+lhx/6BjUsTZWz98=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gsxwf799uo-rzd6atqjts.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-07-30T00:47:58.0827703+00:00"}, "t83ERksDnrbXE2wVZU+uSzK1i501et63vvYKtyhDmpU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\t743c6lr3v-ee0r1s7dh0.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-07-30T00:47:58.0893058+00:00"}, "JJhzwXsmBOTTvggpTRbnty0TOfS9TPxXfyjlTKdm4mM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4759eedip8-dxx9fxp4il.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-07-30T00:47:58.0953064+00:00"}, "Q8znW+ovJRW+bd01a3q+v/fUTeAf4qSITrwwrkSWjbk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zpkawuqkuj-jd9uben2k1.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-07-30T00:47:58.1019327+00:00"}, "z1hiHYQM6CjBsDqdE2QdiPjGhoJpneMQVqU6WzzAsxg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\nuttveq4ss-khv3u5hwcm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-07-30T00:47:58.1079403+00:00"}, "yQRHGFvpCiH06Jzsuc45zKfDtHCiFHtH+Jyz7LF++B8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ea3uvujggc-r4e9w2rdcm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-07-30T00:47:58.1162338+00:00"}, "c04hwiUb/qTHCFks73lpD7I6otrg6gfovalxByab6q0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\altkn17z2j-lcd1t2u6c8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-07-30T00:47:58.1231566+00:00"}, "E88jNz4GHSJMwrAtkHcX+E5pbVVc9hFfZ56asFX1Bpk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\uxwwardfpy-c2oey78nd0.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-07-30T00:47:58.1162653+00:00"}, "9grc/L2P6S+ptwZFETloqKn6AX53j0sSnee29ENKE1Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\x19s93bwmx-tdbxkamptv.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-07-30T00:47:58.1241536+00:00"}, "5nC/h2iHoQanloGhCK1DY/smk2UoodTk5e2Q8ZWQT0E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6ylo0prc3v-j5mq2jizvt.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-07-30T00:47:58.1319889+00:00"}, "l7mHO5EYMi3QajZs8dcQMAYpRCmVP/09B/ELlfeaguM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\efgv4ik39w-06098lyss8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-07-30T00:47:58.1356887+00:00"}, "AMpIXALt1Ty9V7CmnBtwo0aATjEIIslfXdjxjT3zmpE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\f3zypvvg0g-nvvlpmu67g.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-07-30T00:47:58.140191+00:00"}, "PhR1kPK2UyB6b5jDqiHKWbtnkGe/3ZsHSoPcQrW8OYQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\khrz2ekybf-s35ty4nyc5.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-07-30T00:47:58.1452009+00:00"}, "qY5vDTtNQESO7ZOqsTTKGvuOCXnZr7XYEsnGNYTPkzU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\xa1ne1jyem-pj5nd1wqec.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-07-30T00:47:58.168212+00:00"}, "MNcjPH28xlIPsfbVWHfb+936qDCIhXJlMhpXC5AO2gE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hzoru06pbb-46ein0sx1k.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-07-30T00:47:58.1736569+00:00"}, "VHWeo29WImhqT1O7YwD5cFtlSJtKOKkOtR8R0Mctj6c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hbix2erh9e-v0zj4ognzu.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-07-30T00:47:58.1376866+00:00"}, "C6KUhkO14u2U5seE2tMrRHpTagdhpyr3U41a3HDNsTI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\m598h6kyqj-37tfw0ft22.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-07-30T00:47:58.0893058+00:00"}, "kM3/wkZQMVdojSA+gC+xlZpKQA+cNJUcazvRZa/3xss=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vcct4ipxe5-hrwsygsryq.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-07-30T00:47:58.1054337+00:00"}, "B7wThUVnar5boRC4j2Q83cZ2t/x47Cs70uLeWA5r2UU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3hamt6ee9r-pk9g2wxc8p.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-07-30T00:47:58.1109279+00:00"}, "BqmlWznJSFrqchil2CTbU8njT5kDIuynPkNcEWi4pOM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\f0cxvpfsdy-ft3s53vfgj.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-07-30T00:47:58.1276594+00:00"}, "aPrC8tqLpbjpYLnNhgoSpBbNgF7z03sJ6pE4abCaQwo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gj7z6gu202-vpl3cor3y8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=vpl3cor3y8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "gepphjsbrv", "Integrity": "KFYm0L3h1h6nEhc5asI9f2T5sT2k3DosahJdYsOAu0Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44357, "LastWriteTime": "2025-07-30T00:47:58.1391918+00:00"}, "dDHpP5KabEI0d9TUlq7GhutTtawCREdmPKIIJnckUcE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\q94t6u8lk2-m8m04ow07k.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=m8m04ow07k}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "aqxwsrc1l2", "Integrity": "PqV7FoSrUvHZIAI97OB5Du66Rix7At2iTuksxeoo5Gk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92054, "LastWriteTime": "2025-07-30T00:47:58.1556588+00:00"}, "50vfl7zZ9i6VRPD9Sr6PxjVC8fp8HHnmPLiSCx3UxKQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\88l1nx4mvf-493y06b0oq.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-07-30T00:47:58.1616619+00:00"}, "Zj3GcZVF8K4imqKuDhq6qqEMlQXftS+gx1PsHzPiQVY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\pnta6e9kb1-07r8e49zdp.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=07r8e49zdp}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "93kkzvrplm", "Integrity": "S45O8CWj18jAuvMjr7tniBMZLZx6s1gfCjeu1uXTsjQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86959, "LastWriteTime": "2025-07-30T00:47:58.1336639+00:00"}, "mg20SsbIRpuNIk64NJ4+okt9jdbyssufSzhb5phxyiY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3g73q52iuk-tvzo76q7ay.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=tvzo76q7ay}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y7niqziobr", "Integrity": "Diyo0AF1rOw0OhH0uvXj8kXp63NKtgfia/O/8dyayIs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28856, "LastWriteTime": "2025-07-30T00:47:58.140191+00:00"}, "y5fpg8X9wpVJKvyz/NI17FZe/6alP43E6rFFFzE6j8k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\yhlbj4rf35-sybl3t77fa.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=sybl3t77fa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "txlefgy9aj", "Integrity": "kXW7yFoYyoJuZ8DvEVLMROrQ/SMEw2Pbr7LLiGIZlCY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64127, "LastWriteTime": "2025-07-30T00:47:58.1501589+00:00"}, "ER0kZWRS9f0uaeuAaM4W0o+VygHZ/mTZUPivkwj3JnE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zrbarnrtsw-jj8uyg4cgr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-07-30T00:47:58.1546638+00:00"}, "rM9+pj/9ly3kTSG7IakJrQdPTNC50evHjVLhAru2WGQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2sabk1jr8b-u3orf5n9fb.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=u3orf5n9fb}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c5scpwdb0k", "Integrity": "gOJ+qyInHxVGQAHaRs9ggiXfugMTsR/8thKNQLja68A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56671, "LastWriteTime": "2025-07-30T00:47:58.1626589+00:00"}, "fxugI4gnT2HiUWNYEB6scICNQgEYLiDsDTPK8AWYKIY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\lsz5tf7dni-m3in5dwigm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=m3in5dwigm}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ds6ukkp3p4", "Integrity": "3TGnvPeVnXk0b3WXyuF0zFg3BK00M1llWfU8OaGp5wo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29572, "LastWriteTime": "2025-07-30T00:47:58.1687177+00:00"}, "EdAZ1tdPvneZtX3ybL+GB3fzegflTt1YDhxPNiXmzFQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ji6amwcfn2-xxsbxg4q4i.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=xxsbxg4q4i}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pakrqzmxkf", "Integrity": "gQXErDzWkQZSfjEczQ6fcQDBKpT0yn/vH7j1YSexA0c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64429, "LastWriteTime": "2025-07-30T00:47:58.178645+00:00"}, "l33/3Tohrpokr0ubBgf0/RRgTqUekDOosNSe/OF6dwM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zewr3crvez-63fj8s7r0e.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-07-30T00:47:58.1806747+00:00"}, "ZYZLR+jCbbBCSgW3z/TncvT5tsvKx4SsuPqQHwLYDtg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0wfxh5eup3-knl5pustf3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=knl5pustf3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2ojh1hjsz", "Integrity": "nCVT1+BjbwsRwzOPpN1ZSa/iEC2jTv4WkLCtAvNJhLY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55851, "LastWriteTime": "2025-07-30T00:47:58.1326641+00:00"}, "TnLimktWC3AATHKfcCVQJZj/48m1Ud5tz/MXEnLndLY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\dbrkebwbh4-iag0ou56lh.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint=iag0ou56lh}]?.json.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\sample-data\\weather.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4cbitfuojg", "Integrity": "HD3vAUwurZXW96vdgG5RVLhMjmVeSgBs4iKLSw2+Uwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\sample-data\\weather.json", "FileLength": 153, "LastWriteTime": "2025-07-30T00:47:58.0827703+00:00"}, "xtAQh/mBEb9bazMJfUpHZi6oHiHWF2w3MZUf5wmu8xo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2d0fd4ja8n-md9yvkcqlf.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.webassembly.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k1uijd3xue", "Integrity": "aODHHN99ZO6xBdRfQYKCKDEdMXpBUI2D7x+JOLJt8MQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.webassembly.js", "FileLength": 18128, "LastWriteTime": "2025-07-30T00:47:58.0887665+00:00"}, "6vWFOOm4LeELeyi4SBpdQm63PzBkI4Yc+XzsM4ysYbw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\c68o3w32s5-7f970vu3zb.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "SinterBlendingSystem#[.{fingerprint=7f970vu3zb}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\scopedcss\\bundle\\SinterBlendingSystem.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7e0thrd1qz", "Integrity": "P7QPISgTsIAZHudJwEhl1nxlIvSRZkURhXtXKU5M7Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\scopedcss\\bundle\\SinterBlendingSystem.styles.css", "FileLength": 1400, "LastWriteTime": "2025-07-30T00:47:58.0943316+00:00"}, "X8W45xPLGX8lTrgnHG5Z3DoQHpcvhCK+spnvRFHs/7c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vvu7f0gzko-7f970vu3zb.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "SinterBlendingSystem#[.{fingerprint=7f970vu3zb}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\SinterBlendingSystem.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7e0thrd1qz", "Integrity": "P7QPISgTsIAZHudJwEhl1nxlIvSRZkURhXtXKU5M7Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\SinterBlendingSystem.bundle.scp.css", "FileLength": 1400, "LastWriteTime": "2025-07-30T00:47:58.0982989+00:00"}, "HFd7pSVlw7v+bCI/sh/mqoaJ1qZfTC+ZzB2yxVNcG/w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\krd6m83ohp-bvu82j4ad3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Authorization#[.{fingerprint=bvu82j4ad3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lgnzgsjvnc", "Integrity": "hVFMa3gj6Fp9MYa9SnE/zNxKKJm/po/LamQvLk5l5Mg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Authorization.wasm", "FileLength": 18074, "LastWriteTime": "2025-07-30T00:47:58.1039297+00:00"}, "ZFJk8/cWIQQgv6IAqZjA1HpUruZVDU3kjf3NXuoEegg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\5xwuo6sj1o-ptfrz3fits.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components#[.{fingerprint=ptfrz3fits}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nx0qdkw033", "Integrity": "O4SF84iMUCIU777WVOHnz/CBIrJcGjQJ+Sfub12Mous=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.wasm", "FileLength": 135114, "LastWriteTime": "2025-07-30T00:47:58.1173101+00:00"}, "sfTcpLlCDdcuuKyE3IIAg9AMUMb0XAPx949VjEBTJDg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ag0rxmeyry-73oi73dvgk.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Forms#[.{fingerprint=73oi73dvgk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "11xxqegnuu", "Integrity": "i8hp/sm0+RvHSjMM1tC7dQNHAIJknbBR2AAbUcApBj4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Forms.wasm", "FileLength": 16726, "LastWriteTime": "2025-07-30T00:47:58.1246743+00:00"}, "IVrOnfq7ozRlriQX4WaAVJx4H2fkLEUC+HfYlptgg6U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2zs8kpatwp-pm8mpy5cip.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.Web#[.{fingerprint=pm8mpy5cip}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m77mlbd7zp", "Integrity": "/mDirF3iNsajE2C4tMEGSTrDOEJ+fJbLRW38GFBuAzA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.Web.wasm", "FileLength": 72595, "LastWriteTime": "2025-07-30T00:47:58.1319889+00:00"}, "XHmy+XJZDAjQts1AkKoOOdFD623eb82JRlUyOgRgwnc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\qd687wtgfo-4ni28tl690.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Components.WebAssembly#[.{fingerprint=4ni28tl690}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "35l8gvakmj", "Integrity": "B6mP910zomMcEXk9YatOjtAGhi95xzEtrLWhO1ay8uE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Components.WebAssembly.wasm", "FileLength": 67521, "LastWriteTime": "2025-07-30T00:47:58.1391918+00:00"}, "lEskc+TiWkl4NZ98h7utQ897HKA3Ppn6HOvVVm1bS3A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\iu1umzlrwu-eyher82q7e.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.AspNetCore.Metadata#[.{fingerprint=eyher82q7e}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6cciwrafzt", "Integrity": "9toD9OdoDfBmSvIEZvApCUsWcyyHrx7jgfRc7/Oz54A=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.AspNetCore.Metadata.wasm", "FileLength": 2432, "LastWriteTime": "2025-07-30T00:47:58.140191+00:00"}, "N7AT/jfEONItIE6jG8fya3WRujsxgZ4L/fGWhUCEbGk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\lje61wb3gi-itm12vk377.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration#[.{fingerprint=itm12vk377}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ylwie9w1oe", "Integrity": "lkfUFHpai+sDKdcWxdUsiIO9b4ySWvoB5oGiwzWM9PM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.wasm", "FileLength": 15914, "LastWriteTime": "2025-07-30T00:47:58.1436891+00:00"}, "h2y9a8q4IhV5NRY/Eu/N/9wFEZkqiagiSPVIV+fhtV4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6ssjjp8b41-8ewlps0g9m.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Abstractions#[.{fingerprint=8ewlps0g9m}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2ns15tfg18", "Integrity": "PvCs1Rb0KHZb/poJsxWkpi9HESt/zgpwE91ua1WO1lA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Abstractions.wasm", "FileLength": 8470, "LastWriteTime": "2025-07-30T00:47:58.1471879+00:00"}, "esMiG4ewdzkIGyh1givTyn0VHBKtiq/GYipYQwpg7ug=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\co4ngfammm-yr6bnfroy5.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Binder#[.{fingerprint=yr6bnfroy5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ascl1tlgwg", "Integrity": "+ritqzEbel8rw/ph/Qj2wdc0E6T2npQp0aaYshwcP0c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Binder.wasm", "FileLength": 14901, "LastWriteTime": "2025-07-30T00:47:58.1537385+00:00"}, "ECd60pX3D8NpyKuCID38GbgMNVmRq81wp4UGdH4Iwvo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\p60fw9hshe-9nblf8ao5a.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.FileExtensions#[.{fingerprint=9nblf8ao5a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hg7fo2urex", "Integrity": "fyyyTH7FDrDDu+Lom36/MEvOk5x8e7tH8yMo5tUx434=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.FileExtensions.wasm", "FileLength": 8408, "LastWriteTime": "2025-07-30T00:47:58.1581633+00:00"}, "sUEh9OM1s0ifcnytcPeuwDlzFjvFmX0aOBO5ZCOyG94=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\z06bno8pud-mjuqqf9ko8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Configuration.Json#[.{fingerprint=mjuqqf9ko8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "es36lqeprw", "Integrity": "rlVlYp4WJskEWFHzQeBBuSpYe11mHQIUSgTf+6ZyCzs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Configuration.Json.wasm", "FileLength": 8210, "LastWriteTime": "2025-07-30T00:47:58.160671+00:00"}, "lz1T/z8zNqc6DDN+DiwVXn4DHxdqs3utFx1P+o0dMM0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\aoaq9l50fe-v66dtpac4v.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection#[.{fingerprint=v66dtpac4v}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zu3s69rjil", "Integrity": "dQMnjJd9TtuN7I6Wq/MEe8lO5zpD4JkUNGi2LjtVeN8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.wasm", "FileLength": 36333, "LastWriteTime": "2025-07-30T00:47:58.1461914+00:00"}, "y8eTJo/qSHMk4OKtPbKSh85bw5MXnbL3KydFMYFDUXI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gph97rn1wv-apuz8nsfml.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.DependencyInjection.Abstractions#[.{fingerprint=apuz8nsfml}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4f0zd07ryb", "Integrity": "ILbxjNyM+WKKf8bJtkfwEQ0MzZdc8C1xCl0aEGConxI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.DependencyInjection.Abstractions.wasm", "FileLength": 22002, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "Aj9zMOLUchR/vcVIK34bkDUCTjlE/EDND4CYe1DLatY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6gcy130es9-8bt7as0i9i.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Abstractions#[.{fingerprint=8bt7as0i9i}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y33h37kfmb", "Integrity": "/ohYahOQqAYxHvPbrN8O/dEGAno8SeQAhLKP13ljIVo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Abstractions.wasm", "FileLength": 5724, "LastWriteTime": "2025-07-30T00:47:58.0887665+00:00"}, "sqWff2ujZMFgMWVpoM4R720XER6uiiUgT0cwy18hUAk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6ma2ub8nyx-etpym877t9.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileProviders.Physical#[.{fingerprint=etpym877t9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o8816qn99k", "Integrity": "CAKtf+CB4j6FQoA9U4dPbNEkalps572bd+d5kfTgO44=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileProviders.Physical.wasm", "FileLength": 17353, "LastWriteTime": "2025-07-30T00:47:58.0943316+00:00"}, "rHgeKeeL8TnvVbrNfZkDr7s3tov4KLBQGbezRncwwXs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\bubadn219o-g2w0sei4ut.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.FileSystemGlobbing#[.{fingerprint=g2w0sei4ut}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hwhnncxxpi", "Integrity": "YTHGNRLMC1YMeWl9kIacdE49J6KT3ld77WN9Tt/IEZs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.FileSystemGlobbing.wasm", "FileLength": 16776, "LastWriteTime": "2025-07-30T00:47:58.0992961+00:00"}, "CzRujEB4N2bFcWS2T47Lxy2wKWTng/IFUnp40+VanK8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\up41gjk55z-bvn14pws96.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Localization#[.{fingerprint=bvn14pws96}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "66g4pb54t1", "Integrity": "3j+uzjtmX1lwC4MpuV+yMliVB3KiWyDykTBd7obL1X8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.wasm", "FileLength": 10094, "LastWriteTime": "2025-07-30T00:47:58.1019327+00:00"}, "cBvY5jVzjznw36VP4piynmBpWf11CNMocKKovfdr/pw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\yx2jr9c9xx-o4jp2hcm79.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Localization.Abstractions#[.{fingerprint=o4jp2hcm79}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iijz9b5a5s", "Integrity": "di088bLcKqrTdZHmkGePxdnl2/xV1E88Yi4KjByq9Pw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Localization.Abstractions.wasm", "FileLength": 3752, "LastWriteTime": "2025-07-30T00:47:58.1044433+00:00"}, "b3KBJhCZ5QhRSO00A5p7yECYyeE1q1GetvX87uDTZ6A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\h74aegkncq-ul0xzjnwdm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging#[.{fingerprint=ul0xzjnwdm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1bdz5ac3cj", "Integrity": "HTtL0ElIG9s0GrxiaJYcJ+BIfzJL2ovDXBxfPQccf4E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.wasm", "FileLength": 19453, "LastWriteTime": "2025-07-30T00:47:58.1079403+00:00"}, "WKuNZ0jOdfAA+5dwDUfUblrPbFgThgpyh+ydpXXsMj4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tcmwcgwxqe-nwxyu3e2hm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Logging.Abstractions#[.{fingerprint=nwxyu3e2hm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nl2ri78u79", "Integrity": "6xMK6hCgvV14x4djAKXx2ijv4Dej5/en2cdDJZfHK/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Logging.Abstractions.wasm", "FileLength": 25077, "LastWriteTime": "2025-07-30T00:47:58.1173101+00:00"}, "/lCKII9SGjbg9Dtz3mH8FI+g9cPd9E63TbvP/PdIZno=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\lrcq3kiziw-l36scmr1xu.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Options#[.{fingerprint=l36scmr1xu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q10tx7tree", "Integrity": "fnKYyd2gVHBofA0jRPJTWBDeklawripxqtTIwhyTFlU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Options.wasm", "FileLength": 24185, "LastWriteTime": "2025-07-30T00:47:58.1256653+00:00"}, "GXvHR4UUAe5/qbhnZyQrpWEBgxB8g8s6uZYOF2TQRnE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\9lwpyhboje-358c2dzezi.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Extensions.Primitives#[.{fingerprint=358c2dzezi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6xdmz3gb1h", "Integrity": "+p7TJJz9QvNnk6XFeNrWRl7+GS5iOX/R/sE/opnIURk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Extensions.Primitives.wasm", "FileLength": 15651, "LastWriteTime": "2025-07-30T00:47:58.1291626+00:00"}, "3efzYH5DWan7WBYiJEYafMMc8i33+3GDHSaG2bklN6M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\lt181essll-nanjlpvyw1.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop#[.{fingerprint=nanjlpvyw1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yr2xu0mmkq", "Integrity": "Tkqp1lGVMdtmMlVOw/6U3fxmGsCiMa2bmV0MAp6ZU+8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.wasm", "FileLength": 24126, "LastWriteTime": "2025-07-30T00:47:58.1352615+00:00"}, "1IZ4GmIUtdtJPUgCc+iJ8NaQ7c3HdjyjjLzvLPNx93I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\to6jy0u5kj-zjb9sj3c45.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.JSInterop.WebAssembly#[.{fingerprint=zjb9sj3c45}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bvc0vas0f6", "Integrity": "2Rf+0/CumFKPffX6Y4mwn9/ma96biWk5Tun+h9ClDSo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.JSInterop.WebAssembly.wasm", "FileLength": 5814, "LastWriteTime": "2025-07-30T00:47:58.1382008+00:00"}, "XMDUEKlFLqYheXvdkBnUbGgQoGJ+mIcy/965rkTx2dU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\eet9sqojtd-jj4is8psu1.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/MudBlazor#[.{fingerprint=jj4is8psu1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\MudBlazor.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mu4x38gi33", "Integrity": "zhlKcBUEQ7FWJ15GgQhM/0mO8xr+u6j9ZezqU+nbC/I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\MudBlazor.wasm", "FileLength": 2025589, "LastWriteTime": "2025-07-30T00:47:58.4155361+00:00"}, "gGqfgSL81H8g5UwlAzcPS8uePWPLKFJ/cNt2ApXes14=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\u881m7zc15-wwkdnpv5zz.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.CSharp#[.{fingerprint=wwkdnpv5zz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hdokjfh508", "Integrity": "ZZfs/YVvVPlYWANBqHGnsBBJ3yco3TGxvtWaS3piqTE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.CSharp.wasm", "FileLength": 132480, "LastWriteTime": "2025-07-30T00:47:58.4225821+00:00"}, "osWlpv6hvUY10piZ31CCiqj6TN+iH/mDomHe7GKPtqQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hctjhsvmfn-xnsuvdrxcm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic.Core#[.{fingerprint=xnsuvdrxcm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "agj2fwcfj8", "Integrity": "BsIbqKAb8ySltuITZd589QpIGBbHAli61WZpeg5PaLc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.Core.wasm", "FileLength": 171161, "LastWriteTime": "2025-07-30T00:47:58.4306772+00:00"}, "02Mo1xzm2J6d4WqMRR7ctRU3SmXS0rDAPwcYl97cAsw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2wcnvhhney-9dosfnk555.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.VisualBasic#[.{fingerprint=9dosfnk555}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oh4mefjiio", "Integrity": "xCXJgaaArNSwWo78d1TA1onJ+o1I9axun84APFofsk8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.VisualBasic.wasm", "FileLength": 2882, "LastWriteTime": "2025-07-30T00:47:58.1391918+00:00"}, "vGT5fPeK3Qh3GWsDhWEq4blEt/hDcGZwS4byb8U2kRQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ezrlkokxeg-o0bea8efhe.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Primitives#[.{fingerprint=o0bea8efhe}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bdbghfh4dn", "Integrity": "cQVPUtJWU0eJs4RU6t5zhvKF4U7CUoNDi0g9zygytCs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Primitives.wasm", "FileLength": 2206, "LastWriteTime": "2025-07-30T00:47:58.0827703+00:00"}, "ncqWXdUg/jL82DjI/hWcuFVM5q7cL6MoUxeaM4j5do0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gm61t9zfgg-10xfh4xvhd.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/Microsoft.Win32.Registry#[.{fingerprint=10xfh4xvhd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "balm916joc", "Integrity": "vX0dZ9xH/D7Qbqavks/Fao6E8JwbKMmsCutrkfP6Y/E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\Microsoft.Win32.Registry.wasm", "FileLength": 9281, "LastWriteTime": "2025-07-30T00:47:58.0887665+00:00"}, "gB8EByNVHX3eQ+vNIX6GGireWhAfO3YRVsaPEG1Rzo8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ui2d1yx3ih-juaach1ctn.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.AppContext#[.{fingerprint=juaach1ctn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0bzdrk3grx", "Integrity": "/O5SdR5t304gXksW/JgoOfILfwO8Y6Bg45DcU6Wfd5Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.AppContext.wasm", "FileLength": 2102, "LastWriteTime": "2025-07-30T00:47:58.0938024+00:00"}, "zo5y0YGTBajZiSgsqTmkP4/eGYavT/dlkhjBYhp/kZg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\kwfel4ep97-tevaucknsr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Buffers#[.{fingerprint=tevaucknsr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nmdgd9fnff", "Integrity": "/0lduA+cemBkekLM4wKzFLT8czhXEm8BddTJ7RhVBNY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Buffers.wasm", "FileLength": 2108, "LastWriteTime": "2025-07-30T00:47:58.0982989+00:00"}, "5NS4Mh1ZWVECprTTZZY5tFqd8eDvFktUwsSZeinIS0k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tjdswvm0va-db6mieq8cw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Concurrent#[.{fingerprint=db6mieq8cw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00h1pj4gb5", "Integrity": "UDFrQZevb8AJhomkJqeRuLnmw6pNPjju/nYvccon0HA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Concurrent.wasm", "FileLength": 34478, "LastWriteTime": "2025-07-30T00:47:58.1099303+00:00"}, "F9/lpZOvZY1BEt/hK4SEbe+9pvnligrlxBxEHoi/4wg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\qfe22cjrg1-r0oz85e9cl.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Immutable#[.{fingerprint=r0oz85e9cl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5zev0mtj5k", "Integrity": "kQWJG30Z9L1c9cxpYfIfjTDoley4wlLBXiTC6jrUTVk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Immutable.wasm", "FileLength": 100294, "LastWriteTime": "2025-07-30T00:47:58.1282072+00:00"}, "REKrirGMEfjizsUcMslVTpMVRo7KdhFPcoDrLFc2r7A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\sywgq3ozo4-tfini80nq3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.NonGeneric#[.{fingerprint=tfini80nq3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jjsxy3m4nq", "Integrity": "QUMdctPlWyzFIdatqPyHorr3M8ba3v24vz/uLGOQHYw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.NonGeneric.wasm", "FileLength": 14911, "LastWriteTime": "2025-07-30T00:47:58.1319889+00:00"}, "h5Xdm0Khu10MfJQW/5mq0cxpd7NVeQzbBzKNxYRZKZg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\qfhm78hwur-ez39nsje6t.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections.Specialized#[.{fingerprint=ez39nsje6t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z76sejr6pl", "Integrity": "fzjeMQAPYuM5o4UnKPOnJFmy2nGQNcbnHUHuoXu74dY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.Specialized.wasm", "FileLength": 16544, "LastWriteTime": "2025-07-30T00:47:58.1356887+00:00"}, "W57ilChKNzvTHx9x0EsXvsOmVBv7MnnGbv/aGIODpgY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\f8c1xziszk-r26g9n8is8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Collections#[.{fingerprint=r26g9n8is8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l264gm8dxe", "Integrity": "QyBOrUFQsg/Dj4bJA/azFBk/5jVOUcWDc5zm6C2tDAs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Collections.wasm", "FileLength": 49328, "LastWriteTime": "2025-07-30T00:47:58.1501589+00:00"}, "KioBYxjHJtGVtDGXrRpw/wUDJT59nrEIivLGdX6474I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3yy7xjithg-pdn5bck3j7.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Annotations#[.{fingerprint=pdn5bck3j7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tx2nnztgid", "Integrity": "oSEhlezCvb83AulYme8jxrJFzOlHQcxd//Xk3iaTibo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Annotations.wasm", "FileLength": 36229, "LastWriteTime": "2025-07-30T00:47:58.1556588+00:00"}, "wt95Bm1PdqKy5iw9rt7Ud2YtHQPkuG5Wa+APHOTdkBs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ws7vsbmftj-y7prqysv5u.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.DataAnnotations#[.{fingerprint=y7prqysv5u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5gwx2slofg", "Integrity": "haS2Wi+uBO0DEf72Nz0GUdN2QGAbfmho8zMSPl5qWNA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.DataAnnotations.wasm", "FileLength": 2576, "LastWriteTime": "2025-07-30T00:47:58.1581633+00:00"}, "D5zOnz06mKGEF5z2zT4w1GP9JeF5rMX8VWy54/LIwLk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\lcufmjtavq-t9a9wlka3k.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.EventBasedAsync#[.{fingerprint=t9a9wlka3k}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bgtic3qt2a", "Integrity": "ZUKgr0tNC5n8w2ahd4d8TqR6p0Kyvp2GZKUs2NG3lqA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.EventBasedAsync.wasm", "FileLength": 6878, "LastWriteTime": "2025-07-30T00:47:58.1616619+00:00"}, "d0+j9s9DyrGgWEkXoKK/14H5hR0pjOU9GWlDqDuh0kY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vsjvatd1bh-f2y7zqa15g.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.Primitives#[.{fingerprint=f2y7zqa15g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wordwlq37n", "Integrity": "cMEyke7/vVREkaG8I4RtFAMEf/M6XveL/ECKKLbS6Fs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.Primitives.wasm", "FileLength": 13569, "LastWriteTime": "2025-07-30T00:47:58.1649623+00:00"}, "s1qwYEoeyU0IofIP0jxXtNYJ9zk9py0U1fjMWYskflA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hxkk161133-s6d7pax0a7.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel.TypeConverter#[.{fingerprint=s6d7pax0a7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qwi89xuxcc", "Integrity": "uv6Ru+QyJBZkFxOy0tnO0Tql3zJJPuYE3PMdQTwwTDA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.TypeConverter.wasm", "FileLength": 124652, "LastWriteTime": "2025-07-30T00:47:58.1761452+00:00"}, "pzTGYD3K3njpYeEAFMgEihumxkNF9oUeoEJUB6VYYiU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ln4aomdo7o-u0b472gpzn.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ComponentModel#[.{fingerprint=u0b472gpzn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2u190bv2ae", "Integrity": "dhJTX3iT5mD9e6uzQaKx6vTTMYiC9dZtJxWqu8a5mQY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ComponentModel.wasm", "FileLength": 2559, "LastWriteTime": "2025-07-30T00:47:58.1771422+00:00"}, "TJOYcn9MOJCbkoGzq1pnDyaKLynYjHzQwL+maJriaWs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\fidsqvtpw3-kluew6mdyf.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Configuration#[.{fingerprint=kluew6mdyf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dklrniqyb9", "Integrity": "7cT7WrSnLn4BQ274aC3w7M9tgaBgFIK125oW3ltaVnk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Configuration.wasm", "FileLength": 3134, "LastWriteTime": "2025-07-30T00:47:58.140191+00:00"}, "8croQdBB+JZ8rbCrv3OtSwz9hJdB9tx0aX6Wa1quC2U=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\5vgn9d4f6a-vkyu6p469j.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Console#[.{fingerprint=vkyu6p469j}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ezsdh6yakg", "Integrity": "+vwviTbJ28IfrJ5hiyWgdMConOYKac8d5aJPjVpkpEI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Console.wasm", "FileLength": 19995, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "lArm3jUVHx4OJwVOkzXIu6nQqKTFQc6Lc2NayWSWXD0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\55ain44jfl-49bxp5tzzm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Core#[.{fingerprint=49bxp5tzzm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ph2m0aofvi", "Integrity": "46FPZWVKB/b/fD3k9sIC6w8/YPhugcrIeSlJQ1evvvA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Core.wasm", "FileLength": 4595, "LastWriteTime": "2025-07-30T00:47:58.0893058+00:00"}, "OrJOMp8fL9oXtfdcuw6W6XPhC+viybZzU3yoIMryHiM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ib9e0zhuqc-q3ol77wfoq.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.Common#[.{fingerprint=q3ol77wfoq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "x3e3sgo2x5", "Integrity": "LUbFzsO0sZ1dtfD5qgDS+ZcBxmO4khD2xbFfz+cMkuk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.Common.wasm", "FileLength": 378863, "LastWriteTime": "2025-07-30T00:47:58.1246743+00:00"}, "g1Tk/p/ROJxG5Gc4jEiRk1p+yEcGkCOFi0QNwYxs9eE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\t53tb8qezg-zbh2metxpn.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data.DataSetExtensions#[.{fingerprint=zbh2metxpn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0i6m9ynhip", "Integrity": "mBQtniiPVISayeQVVVLO9q6qSs9kZfIV06oez+Y6KME=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.DataSetExtensions.wasm", "FileLength": 2064, "LastWriteTime": "2025-07-30T00:47:58.1276594+00:00"}, "w9pbyjKqljbw6V2OJGtCVE29Df6vN7SONqViwCu4co0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\75mhvvni79-tpf8dxdpz9.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Data#[.{fingerprint=tpf8dxdpz9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zruu4n7577", "Integrity": "T+d0LPPWzO9sJJmuRMM0gRKssUH2lJm+4GUs6yE5DGw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Data.wasm", "FileLength": 5066, "LastWriteTime": "2025-07-30T00:47:58.1301604+00:00"}, "urPevuThf/uUR4aQvK97BFbmaFnpjzgrZwJBXRFirPU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ggjiixb2ya-xe0jinlila.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Contracts#[.{fingerprint=xe0jinlila}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r9k37s7gtb", "Integrity": "oa+pKs1GvxLaQjce+GAzBfGgTZsBk3BCG3J1mtVe6jY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Contracts.wasm", "FileLength": 2386, "LastWriteTime": "2025-07-30T00:47:58.1319889+00:00"}, "5D03x0O0uLmehykZgQSuoHcoVveWcGyQ+2jELCrZ6og=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ufhj1diaax-b54ei8zud7.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Debug#[.{fingerprint=b54ei8zud7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xyj41kfyax", "Integrity": "5+ApcgDs7m+cSWUi6Orflq42nkpyIp4+0fGktDmEFwY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Debug.wasm", "FileLength": 2270, "LastWriteTime": "2025-07-30T00:47:58.1336639+00:00"}, "jn+IVcsEbhPN+9nEyaKUnoNqfOE9SczRfylfPuGndPk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\b3hb903hrd-wk1j35mh70.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.DiagnosticSource#[.{fingerprint=wk1j35mh70}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xj6clzv6kw", "Integrity": "00IscxUZwIWF/qT4o5xUYR7ULVCg2wRKnJYrK9noao0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.DiagnosticSource.wasm", "FileLength": 74369, "LastWriteTime": "2025-07-30T00:47:58.1416994+00:00"}, "FEwEkla5qtrt2SNpfGbK6u6pwvCvDSEkEeYrT56ovqk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gb5h6s5j8k-c8h918kj1s.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.FileVersionInfo#[.{fingerprint=c8h918kj1s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "efustiy2ke", "Integrity": "Cu4Tbrmugk1PMfxl6cmzzbkg2qEB5INxd3fXNjgKIdY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.FileVersionInfo.wasm", "FileLength": 5156, "LastWriteTime": "2025-07-30T00:47:58.1436891+00:00"}, "kPj7XxEs2PWhPOIVNFva7dA1t0OoC72oQ31HN7VMMfk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ls7gf1lfep-vv59ito6pv.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Process#[.{fingerprint=vv59ito6pv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2nkzjagt48", "Integrity": "Pu68ci9VwmRgje+QmusfMtxVeaJetJCr4jQZeukHjvc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Process.wasm", "FileLength": 16550, "LastWriteTime": "2025-07-30T00:47:58.1501589+00:00"}, "uVyhltGf/v0afaNhf/tUVnPS5H/J30RJZO0BeLEued8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zubzk0z0l6-tn3gqtft0g.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.StackTrace#[.{fingerprint=tn3gqtft0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wsukw24ipy", "Integrity": "8ABW6IzRiDYDelggWwsof4HnBrJDmrn4lQsljb8CaFo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.StackTrace.wasm", "FileLength": 7502, "LastWriteTime": "2025-07-30T00:47:58.1537385+00:00"}, "vu+hvl10O7SDQKaaY6KC8Vjn9Xazh2LFrX4MnmHU7qw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\9kh60por1s-wm0g8gvffy.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TextWriterTraceListener#[.{fingerprint=wm0g8gvffy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ofvr76jer", "Integrity": "FPgnCtCYqWlqXjGsXghjj9Ip3ojmFKK0B4Aei1t+D1w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TextWriterTraceListener.wasm", "FileLength": 9528, "LastWriteTime": "2025-07-30T00:47:58.1581633+00:00"}, "UPIaHZ/5TngxVOfdOxvp7vAoxBkzerhgsrR280KOmmk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\uaylvvekhx-h8fqcjxsh7.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tools#[.{fingerprint=h8fqcjxsh7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "95irjfcvvt", "Integrity": "Y1yE7t5EEWOWv++pz33ePMUKNBqHyKHJSGxujrgphtQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tools.wasm", "FileLength": 2173, "LastWriteTime": "2025-07-30T00:47:58.1601559+00:00"}, "n7hsAJgNZ+GK/uYT3eE8APSutuARk+UCVIEW1V8Wgzw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ays2jnmpk6-cz2trkz68x.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.TraceSource#[.{fingerprint=cz2trkz68x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3wf1hbm95", "Integrity": "wvppiJ9Lua3FGUOiHswIK0IxUBmcAOXa78ubgt7AbIA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.TraceSource.wasm", "FileLength": 20407, "LastWriteTime": "2025-07-30T00:47:58.1626589+00:00"}, "eGn7Nw8yM/6rhafCKHBOTCvPpAXcwhZ44p+NCTO4xwA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\jxstfo914w-d4yu0wu953.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Diagnostics.Tracing#[.{fingerprint=d4yu0wu953}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "us5s4xzjdf", "Integrity": "XqSwYIRiiGEnRaFBgVmAHgQYNC7V/ImvUromHxiQvo8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Diagnostics.Tracing.wasm", "FileLength": 2494, "LastWriteTime": "2025-07-30T00:47:58.1639394+00:00"}, "p6t2rVb0Z9xV3e5mEbE4kIPEEWrPIuSxG+687re3BDg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\g7xg214xb5-k8bxwla3m3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing.Primitives#[.{fingerprint=k8bxwla3m3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u1d2kb3flk", "Integrity": "kj4fpIjGtz9P7YCsQfPWqTPNY1NaDppbhp/9kS+bf9k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.Primitives.wasm", "FileLength": 24544, "LastWriteTime": "2025-07-30T00:47:58.1436891+00:00"}, "6K3j1Y6MMuIHYRNe6kN4UxWua8l72fLYngkWOuN4h4s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zy29zz3plb-ym2mp801y1.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Drawing#[.{fingerprint=ym2mp801y1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2urbf5gkk", "Integrity": "/1c/DPeSmeXL0M4IGtFIJNgt5up3hspTydyM5S30bmk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Drawing.wasm", "FileLength": 3878, "LastWriteTime": "2025-07-30T00:47:58.0837668+00:00"}, "xBZrld6hoyrebXE8H8YC22HBaHZhTXNCJBmynM8M0Gw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3poclld8tt-fvt6iv7fue.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Dynamic.Runtime#[.{fingerprint=fvt6iv7fue}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyw74662mc", "Integrity": "aSFnoLCkf+/lBSdvtSkTwt5IE8mH4x7TvFdb11wseNM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Dynamic.Runtime.wasm", "FileLength": 2444, "LastWriteTime": "2025-07-30T00:47:58.0883066+00:00"}, "88o7KBn8Ycq4otHh/BDkxb5KAG8QDlRldDtVffVqjxU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\5ltlhti7xx-zf5zxewa61.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Asn1#[.{fingerprint=zf5zxewa61}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mycx4f1vsd", "Integrity": "3q8mcsXyz6BN3PInLelw1tS4TGDcM3glI0AAkARcK1Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Asn1.wasm", "FileLength": 35954, "LastWriteTime": "2025-07-30T00:47:58.0953064+00:00"}, "a4VTERGU0+muSuO82TZAfxpnFmpt+WUumqLwyaYPi4I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\aaovojw2pf-vm9yj3trgw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Formats.Tar#[.{fingerprint=vm9yj3trgw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mlvpnhbxj0", "Integrity": "xkiMWeSd6CyCY6N+SME5JqK6okcGo1lncp1Qy44vf6Y=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Formats.Tar.wasm", "FileLength": 10572, "LastWriteTime": "2025-07-30T00:47:58.0992961+00:00"}, "OsEtF3g0CsLD1D9og7qxUhD1g4OIc4gepE/blS48VJ4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hsp0tyg63i-0fwt3e8qiw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Calendars#[.{fingerprint=0fwt3e8qiw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u1xubwslp0", "Integrity": "0m3OHGoHM/wti/nOIk7v39Elqn+CvnTza634Zwixcf8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Calendars.wasm", "FileLength": 2292, "LastWriteTime": "2025-07-30T00:47:58.1019327+00:00"}, "963UhjAxEoWIbiniDyn02gLVtD/FxzzKpRtgZVZCa1w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\nsaxkrsi8g-qixeimdcsl.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization.Extensions#[.{fingerprint=qixeimdcsl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bg00xmctsb", "Integrity": "S9uxOirio+8qvz84gkgE3fu+FMXwC5POb1F5X04GeGk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.Extensions.wasm", "FileLength": 2171, "LastWriteTime": "2025-07-30T00:47:58.1064321+00:00"}, "+c0xj8TAvizuJuYsaCm6bMNzP+AR0UqBccdkkVE5W4I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\g5sjk6xjgi-icrjyztu0b.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Globalization#[.{fingerprint=icrjyztu0b}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fkthdlt5r7", "Integrity": "EZ+j/xbfVzuxYKGv68yC+WrUUFiVt6wAGXyb8kkzTU8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Globalization.wasm", "FileLength": 2261, "LastWriteTime": "2025-07-30T00:47:58.108933+00:00"}, "MpRz4Acnvuqq0tfUw3YrMY9ykB0Z8DtAt5vebr2YN74=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\xohudr7td7-wm1biee1tr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.Brotli#[.{fingerprint=wm1biee1tr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tl5ftjpw7i", "Integrity": "3zwknMQNaSmQcdvYv0aJ8TZgb5Y2fAWzaWl7Cl8XRNw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.Brotli.wasm", "FileLength": 7048, "LastWriteTime": "2025-07-30T00:47:58.114215+00:00"}, "hV5VMfXfMdRGxYcNvSG7mW0s0ysVWwu8hCGz/NsXupA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\o8thfz6c5k-ej4yzjlwjv.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.FileSystem#[.{fingerprint=ej4yzjlwjv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bzvm9e86oy", "Integrity": "mRPmXS7yjliE0HCXvfn8kDyROJ10M2YGwTyu+UAXHbk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.FileSystem.wasm", "FileLength": 1979, "LastWriteTime": "2025-07-30T00:47:58.1211686+00:00"}, "p0HKH8LloW0tutDrKzhE641edZ0CzDfqwaLvQCkE4N4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\x348v91iu2-0gu3rcpl5a.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression.ZipFile#[.{fingerprint=0gu3rcpl5a}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5ywqvrrczs", "Integrity": "TaRxxPMFHuUQhXSXkYjexYHrvZfZi26VWdgDcoFKM7E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.ZipFile.wasm", "FileLength": 12722, "LastWriteTime": "2025-07-30T00:47:58.1246743+00:00"}, "4E+53tjND7F0WMsdAj5WT8Tu7gqEW4ACd9u+ao3qUdo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\9fvccmpqmc-dcw20thh5h.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Compression#[.{fingerprint=dcw20thh5h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zysub9w4ro", "Integrity": "K53FeH1uMhUug2nyrovr8108yN321MoTgUMz2NCUsjE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Compression.wasm", "FileLength": 43804, "LastWriteTime": "2025-07-30T00:47:58.1301604+00:00"}, "IyBLLDBKfNRi7kxGS5O+MWstqUyBW2hBDQzq38bR8iU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\13h9grb7vd-mr9kqgonfr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.AccessControl#[.{fingerprint=mr9kqgonfr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nzarwg2iwq", "Integrity": "nY/HjB06ONvezJAR8Jum7vOmuH/lJoLmV+n+ARnncmI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.AccessControl.wasm", "FileLength": 8604, "LastWriteTime": "2025-07-30T00:47:58.1336639+00:00"}, "7OoVWW2VNCtfVrrf205oRtEmDJAJZyD+WPhbXpytmuk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\y6h47mazn4-tmng2ufvun.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.DriveInfo#[.{fingerprint=tmng2ufvun}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h5mruvrxwh", "Integrity": "5VKft5DqffRp0y4TBPqdNNv6yoQ5ClG4i/tE3e9YRDk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.DriveInfo.wasm", "FileLength": 6071, "LastWriteTime": "2025-07-30T00:47:58.1366898+00:00"}, "0db+9cw+lqE/qEJJa2VRfLWv+tzEWIE6m2AmmgvF0x4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\oi7al3n456-xipxlxuqia.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Primitives#[.{fingerprint=xipxlxuqia}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "116ejq3gx1", "Integrity": "+x5EEaS6ELmOuAfC7BYgYX9vYmV+pplt7PJ+VeggWzs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Primitives.wasm", "FileLength": 2174, "LastWriteTime": "2025-07-30T00:47:58.1382008+00:00"}, "r2XMLBvNM02aNqEamul3sgipM1e3MLeqPIr62jkaJuc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\fbw1ova2ab-2pw1y8vrcc.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem.Watcher#[.{fingerprint=2pw1y8vrcc}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "thihxpgtci", "Integrity": "4XyGazgHOAICFiE9M3p18gocIPKLg+7fFeSH6kqYiTM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.Watcher.wasm", "FileLength": 8908, "LastWriteTime": "2025-07-30T00:47:58.1416994+00:00"}, "rnHquxVtJVAevDFGDIDinQ5pYX8uufkC5ty4zVofduU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\873fieetiu-j6u7vtcm66.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.FileSystem#[.{fingerprint=j6u7vtcm66}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bdd43vcon6", "Integrity": "UWQcDmkvtR2nhg2rUeGKT8fofyCSASQn60nHcPCkHDw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.FileSystem.wasm", "FileLength": 2293, "LastWriteTime": "2025-07-30T00:47:58.1471879+00:00"}, "Pet6r+6YTFnqKrANBWe5XERmlxUb1C89eGMIFgkU8g0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zeelk62tuf-k2zd4vdn8q.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.IsolatedStorage#[.{fingerprint=k2zd4vdn8q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o3pune9kln", "Integrity": "bCYCRhSkitrsUiGyVEXh8+0gvGdzHslJb3hfzy+gWJQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.IsolatedStorage.wasm", "FileLength": 9502, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "ATZy48gmsGdEVdmdC/3wjfXi3jGvLQ6jqIo4FeR0Akg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\64iup8seg6-mun4y0k3g1.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.MemoryMappedFiles#[.{fingerprint=mun4y0k3g1}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ui9rq14bt", "Integrity": "ZIVZa+KNAYmzWdfcKNuvJmjNLNVctPyLvQpNWh6rBpk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.MemoryMappedFiles.wasm", "FileLength": 16994, "LastWriteTime": "2025-07-30T00:47:58.0909404+00:00"}, "QOSpQxb5QZ536206WLwmjKx3VWMDKOVGI4ePs8+ymqU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\03tstiwtxp-udwbdnds43.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipelines#[.{fingerprint=udwbdnds43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sd2q70gly4", "Integrity": "cMq79+Q7NQLLYI/yyYzZFNFdFm0E0XLRw0AdgCFXx6I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipelines.wasm", "FileLength": 31014, "LastWriteTime": "2025-07-30T00:47:58.0982989+00:00"}, "4SKXncGqIxiuuO2WLnZy19af3b1l7UNubJz2GLB2kIk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\jddin53596-umfwhvvguz.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes.AccessControl#[.{fingerprint=umfwhvvguz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8c1tln9k4k", "Integrity": "+NJj5RHgOBS9HDnwdRZRyhPTof+MhZC8sH6U7zcPkxY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.AccessControl.wasm", "FileLength": 5652, "LastWriteTime": "2025-07-30T00:47:58.0998058+00:00"}, "i+0gqrtpiRVnATmM03RPHMhCbUCHNR7fNIP+Yy3l2QA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tr8mj3sq7d-t0nelwxf25.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.Pipes#[.{fingerprint=t0nelwxf25}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8v1n72lveh", "Integrity": "NChxeO6sgVMeE3uhytlt9N2viUkvyJ9Qrr4tWDjFWaA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.Pipes.wasm", "FileLength": 11584, "LastWriteTime": "2025-07-30T00:47:58.1044433+00:00"}, "vDwY76ZhPZR5nSRkbnsgKFbbeO80A3YRD7A3N2T3ntY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\m8fpyfsa2y-4x3nc2vatj.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO.UnmanagedMemoryStream#[.{fingerprint=4x3nc2vatj}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "369eggn0uy", "Integrity": "6+WOeyV77axzXZ8BLRWsSM2kvWiZxT1I3Qfu9Lzx0iA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.UnmanagedMemoryStream.wasm", "FileLength": 2202, "LastWriteTime": "2025-07-30T00:47:58.1074288+00:00"}, "KOLj54K++hblQGNiO4fAh95VpKwFcLD64SyRnlRgyhg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tx39m7gc2t-ve06ce93x9.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.IO#[.{fingerprint=ve06ce93x9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "npcj0ww8rx", "Integrity": "5bhaH4ujD0HZCMuBDHdpE3gQV+dwCqCVEZCL2FREmUY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.IO.wasm", "FileLength": 2254, "LastWriteTime": "2025-07-30T00:47:58.1099303+00:00"}, "crJlXMLtnSTDwR9/ZyQrRc42SinGD2p5bXWinPlX+Gc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2lugpar3lu-iy9qiif4uw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Expressions#[.{fingerprint=iy9qiif4uw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1eh2u03ub5", "Integrity": "d61Vdr3nCtDW9Jv09YnWzc4hPQCXEvcD+vOYZ9JA2c4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Expressions.wasm", "FileLength": 217766, "LastWriteTime": "2025-07-30T00:47:58.1291626+00:00"}, "nX67KLIaHT6gx70cEfv0tdxqd0Jj26e4qOzQie4rYtY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\satsr9watn-w5wg4705i0.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Parallel#[.{fingerprint=w5wg4705i0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3h9as4k8sn", "Integrity": "iUXpQ9p4ls28SaYYlIFryvOYuPRIrWngbaLmjLh8A1w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Parallel.wasm", "FileLength": 88001, "LastWriteTime": "2025-07-30T00:47:58.1382008+00:00"}, "Cbsd4HJLvJXITjyd7PfErhwjlX5f7uWFiNgylZZM6Y4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\84aa4numoh-xu7xjw8zxu.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq.Queryable#[.{fingerprint=xu7xjw8zxu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "isrvvii5ed", "Integrity": "jvpWbvnmOWCkmo/xZRI2YmCUETOkbRKuqblZMemn2HE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.Queryable.wasm", "FileLength": 21315, "LastWriteTime": "2025-07-30T00:47:58.1416994+00:00"}, "SM7X6oYDpFCwubAdUCZjeeE1AFReGwzbbvSsTJCjX0Q=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4fk8bzudoj-0mgcdzew60.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Linq#[.{fingerprint=0mgcdzew60}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vts0c3cp1w", "Integrity": "DfaBZrTfIPmk17U8mBlXQZpJPes1CoN9LrB6YNDVCgI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Linq.wasm", "FileLength": 56616, "LastWriteTime": "2025-07-30T00:47:58.1471879+00:00"}, "36efNjP5tosahgmVIfh9FDnBJHSS10fW6l5g6k6x6FE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\q56t9uiusu-jhmbff0p68.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Memory#[.{fingerprint=jhmbff0p68}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bde5eb0l6g", "Integrity": "//GgzZdqrD1SOEZOSQwjLz6I1zkTxHokg4Ks+oGUl54=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Memory.wasm", "FileLength": 21085, "LastWriteTime": "2025-07-30T00:47:58.1531461+00:00"}, "UhgGgtAOIbpzdOTJjw7rmkNmko1af3SlD0kilzQnUsA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ob5nbo4cex-3qsuuerl8f.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http.Json#[.{fingerprint=3qsuuerl8f}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7rrce2e4t6", "Integrity": "ffKZ0DXnEF7P/VCJpMbUu3G8OzJsbhY1BxEG5OyfMv0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.Json.wasm", "FileLength": 19909, "LastWriteTime": "2025-07-30T00:47:58.1591587+00:00"}, "KQEQldG82VAO4j+B7hlkQOp7n9Zpd/BlaQ81iY8OrW4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\qh68o8vqlc-ul8zn5e1cx.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Http#[.{fingerprint=ul8zn5e1cx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wb396zusia", "Integrity": "HIuBzcpDUa5m0PhbkBtHY7B20tfTZ9ar2ISIY0G0Fww=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Http.wasm", "FileLength": 115963, "LastWriteTime": "2025-07-30T00:47:58.1712479+00:00"}, "+3zWJdtCwDSPan1nvT0m9oUHrNf0fzftG0jdM6o8138=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4fkqiuc27h-291ybasbd5.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.HttpListener#[.{fingerprint=291ybasbd5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bhkhd6s4kf", "Integrity": "70b40xCdG34kOT0JjjqU2XjDf+bG0AH6j4NMTzrf1fU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.HttpListener.wasm", "FileLength": 16316, "LastWriteTime": "2025-07-30T00:47:58.1761452+00:00"}, "zVRBEZl4u7kHSFiJAQHJRE16bBiKBRDP5pO0OeKm7YQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\45wbveng1h-f7c6cnpbzl.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Mail#[.{fingerprint=f7c6cnpbzl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "snq03xb5n9", "Integrity": "CILr/MtjKfD3L24Y1rAQBZjGFRHrIkaPWbnegbIMA/M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Mail.wasm", "FileLength": 42456, "LastWriteTime": "2025-07-30T00:47:58.1591587+00:00"}, "75FKvanIgCXMVB4DR4RC/VvT1lt/cSEYk/bCmCJC7QI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vtp5af3ykz-pgdlqf6hf0.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NameResolution#[.{fingerprint=pgdlqf6hf0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9mwxtqts3q", "Integrity": "YdgipYc0oYEcTzMVdXznrUY97CFjJXaMgyRyWB+hUeE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NameResolution.wasm", "FileLength": 5988, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "8h4HKfHTzKFcI01HQ7E7hnJaxdl/8jtKc+5yn6T21zI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\bguffz3t3p-v9f1nrwq71.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.NetworkInformation#[.{fingerprint=v9f1nrwq71}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1l7hvmjj4j", "Integrity": "Wzjyng0YPFT0W/35d9LpjkeJdePM3rbuRQ0sO19nn2g=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.NetworkInformation.wasm", "FileLength": 13043, "LastWriteTime": "2025-07-30T00:47:58.0893058+00:00"}, "99OYFRS00Sm1d/iXLsD/WpIiIUGawp8NEkDHsHFYDrg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\rlumgrc3yo-vco9yp72z9.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Ping#[.{fingerprint=vco9yp72z9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bl4gst9ye2", "Integrity": "wUTwHk5urH3kYfAMw6P0eOJ39TBXWJJ2CtzJgj4zsEI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Ping.wasm", "FileLength": 7663, "LastWriteTime": "2025-07-30T00:47:58.0943316+00:00"}, "VG5hiZlZaA+bC3vWdd34Lb4iza/BE/TzNs7fZL3fs4k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vhaza6019s-3qke94s48t.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Primitives#[.{fingerprint=3qke94s48t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0mqjwdipmv", "Integrity": "iB7+FO1MMQbS0aOUjgZu5Z//ovt95w1gjhY+1ZA+o4U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Primitives.wasm", "FileLength": 46564, "LastWriteTime": "2025-07-30T00:47:58.1008452+00:00"}, "3MBBgBzbpuHcERak631UppBxMHfYHGQHkjHmPcSol2o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\s60fwwe9db-j27d86c1ax.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Quic#[.{fingerprint=j27d86c1ax}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8qga4zvo8a", "Integrity": "MFM7aHt3k/1HPGzkMAYjCG0Samhodry5nM9cpaZIx5w=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Quic.wasm", "FileLength": 11090, "LastWriteTime": "2025-07-30T00:47:58.1044433+00:00"}, "IrnapFR6gdrWsDGBqL4Bw9BGhspoahGAS18HmaxFW8M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gpws7t1cis-3trem8k1q3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Requests#[.{fingerprint=3trem8k1q3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nie1rns9wx", "Integrity": "QUl8IjIg7X/r5FQNy4AZ02sUEr9QhOTWrJQMd20usWA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Requests.wasm", "FileLength": 20755, "LastWriteTime": "2025-07-30T00:47:58.1079403+00:00"}, "FBR0wq0EDLF2B0otyr1W6LseKtUH9BioIbimmCBU40o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\kksfdhwki4-92ksbzd5fa.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Security#[.{fingerprint=92ksbzd5fa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "687z89e0ff", "Integrity": "VrvzlU4BPUQ/SHHmAvHY/IgerOBHR7rWkQr6zbQoaW4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Security.wasm", "FileLength": 33473, "LastWriteTime": "2025-07-30T00:47:58.114215+00:00"}, "6veHCPFHS73lT3oMSM2YPUI8Sg5YXN74j6bZZEDrCeI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4360pmsq75-uobo3ckmyg.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.ServicePoint#[.{fingerprint=uobo3ckmyg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "af86lvp0ri", "Integrity": "JhF0QqP0GyJGw+P+s3xf+YBTKgVBA5517iMnIjZVAUA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.ServicePoint.wasm", "FileLength": 2172, "LastWriteTime": "2025-07-30T00:47:58.1196462+00:00"}, "rVGjfyFbx7OBsWH/1H0EymQBUzLSJaCIdr2iqkX5yLQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\61q244xafd-96hzvc4k8s.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.Sockets#[.{fingerprint=96hzvc4k8s}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tt4svqpw5c", "Integrity": "j0HH7C6VLZ/36zWi39wrNI3evmE9K42Qfcc2ZMR8vqU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.Sockets.wasm", "FileLength": 23474, "LastWriteTime": "2025-07-30T00:47:58.1246743+00:00"}, "ijns7BTg6vLNlNotJQt2qKLS+ksE+cViQryg/gPHIIw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gobz7okpns-2im9s78dal.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebClient#[.{fingerprint=2im9s78dal}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xcy0cqni4s", "Integrity": "Dy74pYDsPmjRTGivTEyKT+m9bJyyo+pPRbQXKYXRGVc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebClient.wasm", "FileLength": 14630, "LastWriteTime": "2025-07-30T00:47:58.1291626+00:00"}, "7RuUHBAlXWSEP4Ectr8OQRSflOEw0RTuzMWTk9gTHSk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ajvzdnvrg5-wgh9g8utaw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebHeaderCollection#[.{fingerprint=wgh9g8utaw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uyu5gtlfv1", "Integrity": "+h8ob1iEqsLdrc4uIuRyN3W4mEG3tlZM17BeUWS8+xI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebHeaderCollection.wasm", "FileLength": 10379, "LastWriteTime": "2025-07-30T00:47:58.1316711+00:00"}, "PV9Be7CJEbMDtp1zy8td8KSSjzoGbx4gISJIQAu7LsE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\997fffbxnt-gd7qi6iakg.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebProxy#[.{fingerprint=gd7qi6iakg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "zp79v8c4r0", "Integrity": "qRFBfKIsAS7QCnDHLlT0C+z+Az7ehuatvS8yBvrXysY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebProxy.wasm", "FileLength": 5669, "LastWriteTime": "2025-07-30T00:47:58.1336639+00:00"}, "2lrOcJH7sbkmBdywztHfgtwiRMY1A8CxkBviu5+nMQw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vt2vsomc73-vjtlqd9u82.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets.Client#[.{fingerprint=vjtlqd9u82}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mf5ap4qr1x", "Integrity": "/hxQbLV04DTK9VRsz2pKhMtJP7bIg7Q9Z4Nu6Dp0m/I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.Client.wasm", "FileLength": 17397, "LastWriteTime": "2025-07-30T00:47:58.1382008+00:00"}, "52286F4PdTJWluyXjUViIcyvdr/GZ41uJ15EvNx9ZkQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\uzqqxsidug-64mxs31z7u.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net.WebSockets#[.{fingerprint=64mxs31z7u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1xfdzd1nl0", "Integrity": "K0evGE2r+HOQOooIpmjBTx/cLaAOll0urWdmfoJ1k3E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.WebSockets.wasm", "FileLength": 39056, "LastWriteTime": "2025-07-30T00:47:58.1426927+00:00"}, "YUwr5nz4ROV5dGFpWHLbcIMqYnAcQ7vYgjFVx6lb3IQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gpkf1t737w-x0ioum7hid.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Net#[.{fingerprint=x0ioum7hid}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2tt7mfwavy", "Integrity": "poTQFqtO33F9Qj7UQVlqmo7FjtWK81l0x70rQajOgLQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Net.wasm", "FileLength": 2751, "LastWriteTime": "2025-07-30T00:47:58.1461914+00:00"}, "eTUqtyB2RIeGXJyvCzeyX4EfMi/OAIv0Ak7P9Axd6hE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\p6muw0zxq5-6p39t98e2q.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics.Vectors#[.{fingerprint=6p39t98e2q}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "glq1n880wx", "Integrity": "5FOgYXFKGW149ppCVxW1Q7NYyzN86U4ISL5vjO3EZhQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.Vectors.wasm", "FileLength": 2265, "LastWriteTime": "2025-07-30T00:47:58.1521495+00:00"}, "UzEkSRLiuZMjlRuQMXoRk2xL7H8VGFLCnplsQaASjn4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6xqq6kusky-mpxwzvv9gh.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Numerics#[.{fingerprint=mpxwzvv9gh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h1xjqty3yt", "Integrity": "Bfw96FyHXkFEojvjqFbic9cn7vG53qKfB83tjxWzsyE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Numerics.wasm", "FileLength": 2018, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "QMl0yKFeSL/+FI8GDJKZjA7Ar0UtGWFCVJFb/PSiD4o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\gg5qbe8slb-0h2ofj4d0g.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ObjectModel#[.{fingerprint=0h2ofj4d0g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9ri5hwzubr", "Integrity": "Nc+ttMEc5A5GlGGRur65BbVDF8dMFFCCWuEaKO5XLVQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ObjectModel.wasm", "FileLength": 13591, "LastWriteTime": "2025-07-30T00:47:58.0887665+00:00"}, "7zeOlp3gDPQGWuy5Jo/HlfmIL79vWntYgfSqHo5iPlc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0auvtfl02y-s8o48w3zhs.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.DataContractSerialization#[.{fingerprint=s8o48w3zhs}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nnba5dymox", "Integrity": "fY34YuVv7gCoa6TcvSOEsC9pwyj7KPMpjGsMAVXih4E=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.DataContractSerialization.wasm", "FileLength": 304579, "LastWriteTime": "2025-07-30T00:47:58.1186422+00:00"}, "qzCoFn3zfYEnsZ21XsBcx93kFwYKwNCtRHUtkRDSIuE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3uf55pfz0h-u8z9sw3duu.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Uri#[.{fingerprint=u8z9sw3duu}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rgvz9leaei", "Integrity": "CwcXqtfZeLRHpA2b3uqzr2Boq+zHKxU7+rfMrgSnQWU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Uri.wasm", "FileLength": 42193, "LastWriteTime": "2025-07-30T00:47:58.1256653+00:00"}, "hZloCDhpWa6zzpMzzbIcGoulVLp+AGCI564oqNYZzO4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ijhmral1zz-nw2es9etws.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml.Linq#[.{fingerprint=nw2es9etws}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcxiu2fvdv", "Integrity": "VE/YwJAIh39/F/VAGsSVUWlJjbHBgasWFCPli1mKQmk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.Linq.wasm", "FileLength": 59684, "LastWriteTime": "2025-07-30T00:47:58.1319889+00:00"}, "VkFwnbyo8y8MFg1OBROSw7oiMdfql1SovhvjStlXBx4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\18lrnsrkvx-ceekp79nva.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.Xml#[.{fingerprint=ceekp79nva}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "67nwfsh5yw", "Integrity": "cuxac7TkQAMUYalbTU0tiPvRrQzL/UGf/m4ZVbOBYqE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.Xml.wasm", "FileLength": 1069743, "LastWriteTime": "2025-07-30T00:47:58.2268198+00:00"}, "9KhbpMFlAmt8f2lz3yknFBAMGF+2CLqO9NMOnHtg62w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\lvrhfpfd9t-yuilq41vqn.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.DispatchProxy#[.{fingerprint=yuilq41vqn}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bftxex63a2", "Integrity": "ydoK62CygOTlEJQK2FXVq2HliKr/I3RDeD+B5inAJ2s=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.DispatchProxy.wasm", "FileLength": 13142, "LastWriteTime": "2025-07-30T00:47:58.2288166+00:00"}, "RgaX3JUbZIzy5fYC3oYNlHmxTZ9nFeo0BzQvSwpz5n0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4rp6h4ry1x-60tb0ho2sl.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.ILGeneration#[.{fingerprint=60tb0ho2sl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dvedu3yeu2", "Integrity": "dwko6ysfXGq60a1g6ltCc8ctY4BIcVpT8zghxJURkM8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.ILGeneration.wasm", "FileLength": 2268, "LastWriteTime": "2025-07-30T00:47:58.1501589+00:00"}, "BWGDSEJouoHrq1lI9wN3M3SW2+xLWeNmxdkoWG0CdyY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0scg11qoya-lkjxsh5fdq.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit.Lightweight#[.{fingerprint=lkjxsh5fdq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sa8azibqaa", "Integrity": "hANCrMk4OmQFnXPDAmtn5i0lrBWQaOfYC2JWNhGhT40=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.Lightweight.wasm", "FileLength": 2224, "LastWriteTime": "2025-07-30T00:47:58.1546638+00:00"}, "T+hSknmWBEcBpGoLawuN4id1RPUCIEvmxQ2y6dLOG9M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\803actyyme-mntt6z4my7.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Emit#[.{fingerprint=mntt6z4my7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "llcc9si12z", "Integrity": "ycN0mYzZm7g63N+ppL2FVOPzvwmHWtppS0OfMi99leI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Emit.wasm", "FileLength": 52820, "LastWriteTime": "2025-07-30T00:47:58.160671+00:00"}, "qftHf2vSr4UADe+VxemPv5FKX/OK49MGd0vamUz58zQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\q2ehz80aiz-qcz580dp2c.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Extensions#[.{fingerprint=qcz580dp2c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dv15miewx5", "Integrity": "udYG8ggsRHAEjLpTdH41Cx7bJ5+eulbUbCwgE9es7Mo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Extensions.wasm", "FileLength": 2138, "LastWriteTime": "2025-07-30T00:47:58.1634238+00:00"}, "5LusV/xtJe2NWyctlDR0kv+UvdOrfjUWSsvD/bUVNSI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\7klyqrcpz8-qlfabz3vrd.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Metadata#[.{fingerprint=qlfabz3vrd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dc32nqmwkg", "Integrity": "kwOFp39SPsDj6wMO3BqNzffgt2GR8T/kcFvrsbHukKo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Metadata.wasm", "FileLength": 195557, "LastWriteTime": "2025-07-30T00:47:58.1796656+00:00"}, "c9WOfrbtCXkWB2LYec7/zgsVNTKMaQ2r66Xwd0MhB7I=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4qg2l5wnoj-4ioali3dtl.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.Primitives#[.{fingerprint=4ioali3dtl}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2n0jv2kwtw", "Integrity": "/pAfLArMOkFBu/7hm7HuLrZdQotSd7UGsXt8h8nP9o8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.Primitives.wasm", "FileLength": 2364, "LastWriteTime": "2025-07-30T00:47:58.1806747+00:00"}, "vFFWSrCi+9i3D3miGGbWgfd221hO5uxXkOxzXhamf60=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\d3w54k6qg8-94422c2jz8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection.TypeExtensions#[.{fingerprint=94422c2jz8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "h073v3gg3b", "Integrity": "QbEARK98QV9SEUkBvuKpGZIyYzAHRftNsr92t8Ruzk4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.TypeExtensions.wasm", "FileLength": 5729, "LastWriteTime": "2025-07-30T00:47:58.181685+00:00"}, "YtXAdQwgOq9RS9arxt+gEDuSXgrheKKGyuhdVZ8Gl1E=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\30xbnqce0t-az1ws9l8sb.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Reflection#[.{fingerprint=az1ws9l8sb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5o50pcjuxv", "Integrity": "S8o4cA1C8ejxdJtjvzZLju0jKbvQOUjg7JA3Z2ruOl0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Reflection.wasm", "FileLength": 2458, "LastWriteTime": "2025-07-30T00:47:58.1837054+00:00"}, "WHZpJFEq2dLuUmyONaR74/3HPVutuDUnRMl0IvhRDUY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\10mon09ida-7crqeatgdf.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Reader#[.{fingerprint=7crqeatgdf}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fyqfqm9e9v", "Integrity": "yUdcgJdKOuPZ33UzoxeqrvhuBRlU0aAV8ahlqhKT6NY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Reader.wasm", "FileLength": 2110, "LastWriteTime": "2025-07-30T00:47:58.1847162+00:00"}, "mcRZWP79hTm10sCF7T8owcOc6BtL7zczSBgxZFTVSqg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\sb0a9nqlz9-vhjl4emd1k.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.ResourceManager#[.{fingerprint=vhjl4emd1k}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z35wpaxyq7", "Integrity": "35KNIlbLUtgUDRl6rqPC6RDRyXazSF2SuR9kGNRKVKw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.ResourceManager.wasm", "FileLength": 2233, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "2qTWwjbwRimV1mx7w7C0CQkbgpGqx0hEVcMByjLccqc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\v3cycknx4w-g2ueqliklk.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Resources.Writer#[.{fingerprint=g2ueqliklk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tclydei771", "Integrity": "Y1qXilPSAo6SviPp4f22U0ieW9/RZEn0MRXpAOJ3oPo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Resources.Writer.wasm", "FileLength": 7740, "LastWriteTime": "2025-07-30T00:47:58.0887665+00:00"}, "356Hq+fKGVKvQcvCAXA0P7DaNAAw2qgaFgTNSbU3zmk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\dure4kbb6p-tikfkmjbfm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.Unsafe#[.{fingerprint=tikfkmjbfm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ya1qjs5sc3", "Integrity": "GrylQAAySnRiGYeW0X9MFj9puJqLsYHkZ//pFe2HztI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.Unsafe.wasm", "FileLength": 2123, "LastWriteTime": "2025-07-30T00:47:58.0938024+00:00"}, "TYB+VK9U105l/yJCgnWPqo7OuAL1CXpGiwffmyCRqJE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\47gk3lpquh-ikre56ww8x.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.CompilerServices.VisualC#[.{fingerprint=ikre56ww8x}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3au4i8d7lx", "Integrity": "S3Q2I1QtC0t7SRk7ynIaVXQiuP0YMHG5bbP5LPo8nKY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.CompilerServices.VisualC.wasm", "FileLength": 3072, "LastWriteTime": "2025-07-30T00:47:58.0973014+00:00"}, "XMnsPU3aMNG/wo5vrrVovw8jwD9l/kVw25W1v8qqvno=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2mcdlbcgom-d774vcz111.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Extensions#[.{fingerprint=d774vcz111}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "loy83zw38q", "Integrity": "a4/rXPQLKsj0x/aPmGBFQSE2a2UV6LUZokLDON5ZFTc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Extensions.wasm", "FileLength": 2998, "LastWriteTime": "2025-07-30T00:47:58.0998058+00:00"}, "fD0dm/GampxCwUq3RK3OfZYG73DGgyYIHDBzAIWJExY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\13mh3lh624-udw3yppzvg.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Handles#[.{fingerprint=udw3yppzvg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "muv3ivgs38", "Integrity": "/vHMa+0QRJ0ZcJejJnCvySA1OTNkGRGVcKjkd+2qChk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Handles.wasm", "FileLength": 2194, "LastWriteTime": "2025-07-30T00:47:58.1019327+00:00"}, "xpFSryNcZ58e4WCedHO9gPJqj9hPxzOx7UPaOE5P/3A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\aehdv2ytbj-ccb9zx17su.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.JavaScript#[.{fingerprint=ccb9zx17su}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e8qxqfwfvx", "Integrity": "Drn8vHq9DX8Km32OXRnF+IeHl51FbewWQYFweiIZpps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.JavaScript.wasm", "FileLength": 31670, "LastWriteTime": "2025-07-30T00:47:58.1064321+00:00"}, "7j2HfidQsls6FMYWlrwicpT002A6EMbFiDQNHFoLZEg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\dwzoki8cyo-n0wkls2ql3.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices.RuntimeInformation#[.{fingerprint=n0wkls2ql3}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "pt6jcia13b", "Integrity": "TZ2kRBZvtoEh+uARKAidIYlxQgXnejSTwiPEh+G0FMM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.RuntimeInformation.wasm", "FileLength": 2145, "LastWriteTime": "2025-07-30T00:47:58.1099303+00:00"}, "MUsgtvD6k4DXMmW2sEl7RrEhv4MNaCSVpoFb8iQW6Rc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zrbia7qh11-kg92xkhxu6.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.InteropServices#[.{fingerprint=kg92xkhxu6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jnkrnq0p39", "Integrity": "sf/+vbW6ZLv3MLcmrSuxiGe9ViMwZtTB4c24RZgUkY8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.InteropServices.wasm", "FileLength": 23804, "LastWriteTime": "2025-07-30T00:47:58.1186422+00:00"}, "NZiEYMWEDlvl4Dt5lti+hrqX5HbZ4ol1UNvASDIpZPo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\5f93x12hxq-ep140l5t7u.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Intrinsics#[.{fingerprint=ep140l5t7u}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uql1hvtnh1", "Integrity": "qr4PEIMVe83DjyHmAKXscyvAmPRRpAdZLlTt7qQ/EPE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Intrinsics.wasm", "FileLength": 2744, "LastWriteTime": "2025-07-30T00:47:58.1231566+00:00"}, "T2K+K8z4iUYRkoDiMQpcJuC/CMhOOB3qdeKKEvJscVA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\b6p6dx0zug-0ytrfmq3yo.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Loader#[.{fingerprint=0ytrfmq3yo}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xeu016noi5", "Integrity": "B31flwMOEErIx2H7tG4oSfFiXC9Ebur9lO9xOxRdZUw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Loader.wasm", "FileLength": 2315, "LastWriteTime": "2025-07-30T00:47:58.1256653+00:00"}, "6a23Kw7nVJ4X5vyzD29GC+m+HosaVWFSWgsGMNt898A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\rvhvrjnzwh-4jdlfxag51.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Numerics#[.{fingerprint=4jdlfxag51}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "s01svwwb9c", "Integrity": "LLMKXADv6uYLCLv5UWeYRDEcv7bs9+53PmFIyCpxoeM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Numerics.wasm", "FileLength": 53383, "LastWriteTime": "2025-07-30T00:47:58.1452009+00:00"}, "QlaOYRagHHbGegxc0FZxcNPEHlu4/Qrf4A7oGEN14CI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\qu2acns1xu-8hvyc6n71g.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Formatters#[.{fingerprint=8hvyc6n71g}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cdb9pklw95", "Integrity": "MIUy6a5E7Rjbbbg2OjTL+yS/JOxbUZ6BLfgXlPwXbzo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Formatters.wasm", "FileLength": 24578, "LastWriteTime": "2025-07-30T00:47:58.1536598+00:00"}, "iOsNfV9HNoPET+W1iIHUBzJBdefK8UrbtHBPRun9Fdw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\99kcuywm57-3zz321dhx5.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Json#[.{fingerprint=3zz321dhx5}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3ozlmo18n0", "Integrity": "LRKVFX02AgH2rkO6grPJm+EEBv43fx2u/QAAB/k3SA0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Json.wasm", "FileLength": 2248, "LastWriteTime": "2025-07-30T00:47:58.1571693+00:00"}, "mCtfT5sI9KYxisiUgzWXI9fXWDYe2qgHRT7E+tVn7lo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\7kzq7almjw-b17xem4kd9.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Primitives#[.{fingerprint=b17xem4kd9}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2ypiyfobw", "Integrity": "wYLMDnzJLsJKq4sz9YsQbJu5yU47v+ndihXyOqMLUzA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Primitives.wasm", "FileLength": 5497, "LastWriteTime": "2025-07-30T00:47:58.1591587+00:00"}, "dCiXVa3DGT1DpaFWMqe1zpvpENwXS4x8KRq1eOmI/p0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\yo7nk4z2rt-7c8iwi484h.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization.Xml#[.{fingerprint=7c8iwi484h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fw2ne0xq80", "Integrity": "b8wyFEACMdEscPnzgCC61GNNGRbVB5uuJQxZDdY2g6c=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.Xml.wasm", "FileLength": 2565, "LastWriteTime": "2025-07-30T00:47:58.1521495+00:00"}, "63E9KxHKYtkf5I7J/a7THHg1NlzxLLeSDqMUTz1thdM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\7yayh16ce2-joa9uzwb91.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime.Serialization#[.{fingerprint=joa9uzwb91}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qbtk3b453n", "Integrity": "ir/ctf6FFvLx5YhjpbXYlKYaxX/iT2BK57t3JqSu3Hw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.Serialization.wasm", "FileLength": 2497, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "zGYOn0GYYhtGAfGoFaIbTIYj7Jnkk/HJLOsLRoRBAkc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\4vv1urpn2c-ds6ouyv99t.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Runtime#[.{fingerprint=ds6ouyv99t}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oazov0fexs", "Integrity": "UEjpZjTbWGaXf/umYLCOgHDyqjQLmbrVNNK8I+D6DbE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Runtime.wasm", "FileLength": 10726, "LastWriteTime": "2025-07-30T00:47:58.0887665+00:00"}, "MoSIsD0rCl74XXKynd8szqj26Wnis3O3EqcEaomz21s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\h1xbmoiwg7-7hjfpq508c.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.AccessControl#[.{fingerprint=7hjfpq508c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l3zjosnd9f", "Integrity": "cfDXgQvSCitVf4a71W3Gd0hu+sDFWdKsY+0jtUiWo7I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.AccessControl.wasm", "FileLength": 17223, "LastWriteTime": "2025-07-30T00:47:58.0943316+00:00"}, "odywdd0GwxGSoMcXVRGAlPcydibKZl3XzapLB1c253M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tsr1d9n3mx-nli2l5xz80.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Claims#[.{fingerprint=nli2l5xz80}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y86un4gjyg", "Integrity": "IkJPxBACo/9dnU2HTT3j8Gy96xQcLkXUx5K41HYsMS8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Claims.wasm", "FileLength": 16445, "LastWriteTime": "2025-07-30T00:47:58.0982989+00:00"}, "sxm/GwD4P+tP3kfanpg40PArm9mEaPkolpfXntbfhds=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\sqit4fg1xl-mdf98ysb2r.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Algorithms#[.{fingerprint=mdf98ysb2r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jaqz3k32wo", "Integrity": "29Jk9SNBrxdXOriN3ZwalBIlXJNLCCV3IfIxYSoRutI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Algorithms.wasm", "FileLength": 2716, "LastWriteTime": "2025-07-30T00:47:58.1039297+00:00"}, "1rh4zuMhUB0FwA7pYulV9nI6lbQhd4mV5tmUtKPTky8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\5q3m82na3n-6pipl4ncvr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Cng#[.{fingerprint=6pipl4ncvr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3andy8v5jk", "Integrity": "G2kjnZzQf6E1R7Jmr05Hke+A5Lz+WewRqwhwmXN04Ts=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Cng.wasm", "FileLength": 2476, "LastWriteTime": "2025-07-30T00:47:58.1074288+00:00"}, "OAmmPiyxq0igbtqjWgRJTCN5cdWCYppN6ll1p9EDAco=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2pey11jlt7-3j5ancst43.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Csp#[.{fingerprint=3j5ancst43}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vlyu1n8atc", "Integrity": "TZgXzbzju6PRuLs/RDL9SQP3U84OOfanWROaCP7wcBg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Csp.wasm", "FileLength": 2337, "LastWriteTime": "2025-07-30T00:47:58.1099303+00:00"}, "w7UCX4ht4CvR1/HfwnFzQgAEL9v+I1sJyw3tyfF+qkw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ok6pzbv6j4-3fqk27lesd.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Encoding#[.{fingerprint=3fqk27lesd}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oqtusg75iw", "Integrity": "EH5wbvAxJWVtpsPJpeyLo8zQfbkGq4YX9bYGQxM7L5M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Encoding.wasm", "FileLength": 2273, "LastWriteTime": "2025-07-30T00:47:58.1152284+00:00"}, "IUEsNb0NG3yp8JrOjkMM2ZBxR9L/cf9KIVpXkrnX/IE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\mkx6uelmpk-dryrlehyhk.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.OpenSsl#[.{fingerprint=dryrlehyhk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "5vb1kaiesj", "Integrity": "UY46syzgH794lJnIGf1gx/AO9hSLG+Brhdzzyxc7YBY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.OpenSsl.wasm", "FileLength": 2207, "LastWriteTime": "2025-07-30T00:47:58.1231566+00:00"}, "v2AC1eXgutQvBPmJs8YW6MABn9/x5j/iK2d1ZFrT0A4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\d7j1blywv3-q27tawox7d.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.Primitives#[.{fingerprint=q27tawox7d}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "614v9as72o", "Integrity": "KreiRgpky3IQQBlU6zdm2OaSDAJYTUqGNmIAJ3mWSwg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.Primitives.wasm", "FileLength": 2332, "LastWriteTime": "2025-07-30T00:47:58.1256653+00:00"}, "AOPTU5MFmMY3uc9EsFBSsIY3qriY4mGVAfMb/kRo/8k=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\8iwdg3mpfl-co86r5at5r.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography.X509Certificates#[.{fingerprint=co86r5at5r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ombutrwjx5", "Integrity": "YN/BKrbDJycg8Y60lf7qTPgJpYl2dApbBR0nSwxh40k=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.X509Certificates.wasm", "FileLength": 2671, "LastWriteTime": "2025-07-30T00:47:58.1282072+00:00"}, "jpBFwQrS5HOXzhO7HIsv4VZxjKwhJE/dX3IllGDUslk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\w27znd4vhw-m5z4m57awq.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Cryptography#[.{fingerprint=m5z4m57awq}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hf20jcczg2", "Integrity": "TS+GAjSPWVoTXtAS/1u7ZCa6mN58xIVQlgouZN/G6OQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Cryptography.wasm", "FileLength": 192147, "LastWriteTime": "2025-07-30T00:47:58.1546638+00:00"}, "Uu6wYVpVu/bwtuofYK6gnie96UtApN/T+UAUwMIRCj4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\m9r2h0tda6-gba50v5uxa.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal.Windows#[.{fingerprint=gba50v5uxa}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qy328yrf6u", "Integrity": "bj/9uSMyBUnpFI4hwLBCDH7rC4FcvLmuAUPJc8RmU0M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.Windows.wasm", "FileLength": 11372, "LastWriteTime": "2025-07-30T00:47:58.1591587+00:00"}, "AaAIhfQU+P9NF2G5fk91gUfsFECIRvdJSI0gUxlX0f0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\mkr5zwpvp3-3x4i57jlr7.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.Principal#[.{fingerprint=3x4i57jlr7}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oy5cthv1e7", "Integrity": "MPp/yJezRmZ/cXkl7Feb/0JfNQ/rNIyrr5NiXVVWYas=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.Principal.wasm", "FileLength": 2161, "LastWriteTime": "2025-07-30T00:47:58.1616619+00:00"}, "wTOMpwLDDoT9glYnki7joC7nh7xcbodC2cpUdCat1og=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\6g5gy7j4ns-22pvy7xdie.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security.SecureString#[.{fingerprint=22pvy7xdie}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "di1cn2axvh", "Integrity": "W870OeqxVIWRcpsNWq68o1t6FlAkKmASHw3UWLXCeWY=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.SecureString.wasm", "FileLength": 2183, "LastWriteTime": "2025-07-30T00:47:58.1639394+00:00"}, "9S3yJ9GHtaGMG7BKiP2y+/9ubRQJ2QnJ8oyNkndblJY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\zzrgmxdc7c-0s30uo9nq8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Security#[.{fingerprint=0s30uo9nq8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wnodl0vxel", "Integrity": "HCP/Mn8Ogy90OYgdjux4SSHMmhq+UG+bL7s3kaMCSOU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Security.wasm", "FileLength": 2973, "LastWriteTime": "2025-07-30T00:47:58.1566559+00:00"}, "+EBErKvfxcIfFsl7jsh2P2pbemqJGzZvMNcG5E9ccjM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\i8evohxi9u-p2hu7rgmvt.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceModel.Web#[.{fingerprint=p2hu7rgmvt}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nedjssis6y", "Integrity": "YYmejec5deDi7G9tXnT4qlFOzmgEMpTqSBpIQkytXmU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceModel.Web.wasm", "FileLength": 2539, "LastWriteTime": "2025-07-30T00:47:58.0837668+00:00"}, "OHsUkU/eYhtKpRcTP6PRLj1i1jAQhY6ruikM/SgBx8s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\tkn11qyuib-9mp73ffmth.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ServiceProcess#[.{fingerprint=9mp73ffmth}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1wa1luzpx2", "Integrity": "YcrG+ietmoRw8UuAgwlJIj0uMCQ7VF98wTU4YpWD0ig=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ServiceProcess.wasm", "FileLength": 2295, "LastWriteTime": "2025-07-30T00:47:58.0874888+00:00"}, "fF1bHjrzvqnZC5sYV0/1uF4BLuNQg7y9DwzbltWsImI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0f03zf9ayf-7yh9p39jmr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.CodePages#[.{fingerprint=7yh9p39jmr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "brgk5nsn76", "Integrity": "1/LPP8pO+211QMX5MqvxoqLGjisMnm5LYbzXDZnIxuc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.CodePages.wasm", "FileLength": 518372, "LastWriteTime": "2025-07-30T00:47:58.1196462+00:00"}, "9Ni+IT2cDLGDkMWezCiHvK4LN51fXD8rBiecckKrp8o=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\bp7dbmhqlj-6rnks9ilk8.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding.Extensions#[.{fingerprint=6rnks9ilk8}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "itelgb3h17", "Integrity": "ujXl4ZbV8QCNB0CztjAXoIjpAqiEofM7cV6OUwDLuUc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.Extensions.wasm", "FileLength": 2254, "LastWriteTime": "2025-07-30T00:47:58.1231566+00:00"}, "0kESHV5HLn5+4CwARwFuhjmVd4vxVpuEeUvRF7KlVhQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ithy78ykg5-se1td1q3c0.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encoding#[.{fingerprint=se1td1q3c0}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j8y4y54lmn", "Integrity": "rWOmk8+C/FLZ9GKqaJOOl+OWxltlOomFz+gxpQbdCsc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encoding.wasm", "FileLength": 2229, "LastWriteTime": "2025-07-30T00:47:58.1256653+00:00"}, "dsu1CkTcDL3glj46vO2yFOqhtFGRMImA651rTxbLltw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\btnvowsljr-ddpjqtd0cx.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Encodings.Web#[.{fingerprint=ddpjqtd0cx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nsd1egty9l", "Integrity": "6aW3NXCThIv1cF/PlqmtalnoHK0jk6dN7PwfBqypxXg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Encodings.Web.wasm", "FileLength": 23951, "LastWriteTime": "2025-07-30T00:47:58.1291626+00:00"}, "4NrZi6wsqhZ74m6SrmX0aNLzZ1RhBYk/j/M31ItdBDg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\8etmoj10ce-5spx91fs77.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.Json#[.{fingerprint=5spx91fs77}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hkqf827zd2", "Integrity": "QbSeLzR8Md8iGg7lUkgVnvJ6VidR839qVVMXl0yMwfc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.Json.wasm", "FileLength": 221091, "LastWriteTime": "2025-07-30T00:47:58.1481856+00:00"}, "gcljIYPTFQaihSFu1hY6UxSJwPCIhJqFO6Q7CzuzgTk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\fcedr77bon-9ma84zffbe.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Text.RegularExpressions#[.{fingerprint=9ma84zffbe}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rmrufsoasj", "Integrity": "Y0Ygaj28b/hLDZMi5kNY0Sx7BbndCnKu1UwzC/sdZrw=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Text.RegularExpressions.wasm", "FileLength": 156842, "LastWriteTime": "2025-07-30T00:47:58.1626589+00:00"}, "9bOVir8EYsxOg7H/kE7zJK9VELBF8CnWwB+033J9NYA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\e0bu2jtnb3-ncb64ukzfv.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Channels#[.{fingerprint=ncb64ukzfv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "81u69qfhp3", "Integrity": "dKEGoM3lfYO0STu826GEZLr8ovk1ZrD8LVeEOH+eors=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Channels.wasm", "FileLength": 21010, "LastWriteTime": "2025-07-30T00:47:58.1662281+00:00"}, "rbQDTOnLQxlMmi2DKVDZn+AJ9Knf3K148QrUmDs8PqY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\okp1qwc73f-bfemo0ymlw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Overlapped#[.{fingerprint=bfemo0ymlw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i9ofo53ot7", "Integrity": "lqRjg+K1wopJqL1ptLPC9iB53+2AUK4BHJvk2MEGheQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Overlapped.wasm", "FileLength": 2304, "LastWriteTime": "2025-07-30T00:47:58.1672182+00:00"}, "u5Lry5svmjZMqgguQdG+n2huDHPtKIN6944UmqXP1vk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\an4zn8zatx-ldc2uat7ti.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Dataflow#[.{fingerprint=ldc2uat7ti}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o296xuce8i", "Integrity": "cKyNk7jcrTYhKPEXpvs4mQSRsEkqUWS1r/nPB6l+X/4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Dataflow.wasm", "FileLength": 74083, "LastWriteTime": "2025-07-30T00:47:58.1736569+00:00"}, "fr7/O+To83tRTzBHYCr07KJE3PaCoviGTLOuguMJZUA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\efzw2ywbvh-cjzqcnpgaz.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Extensions#[.{fingerprint=cjzqcnpgaz}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oxeaf24g6r", "Integrity": "5J6wFX2l0E/sTg/5zPjX+EuZ0GGjA/PSeMDzA91erjM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Extensions.wasm", "FileLength": 2303, "LastWriteTime": "2025-07-30T00:47:58.1751452+00:00"}, "+4kaNkZqRXavHa3KcWCR9fYkgqA7Dz3P/q2jCcfc1uA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\7f6l933cyu-adppmfa22c.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks.Parallel#[.{fingerprint=adppmfa22c}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "08wvxj48ge", "Integrity": "IB5LGNz5z37iB0LR7FXXxXuQmlj6aq0NOPCw/Q+HtGQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.Parallel.wasm", "FileLength": 21519, "LastWriteTime": "2025-07-30T00:47:58.177653+00:00"}, "A5pF9bNFZ9kE4rJ6vbHJivjLVUhkRhCL5eI9PSQ/xX4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\l2reo40vfs-tqpw82krhi.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Tasks#[.{fingerprint=tqpw82krhi}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j3ff208rr0", "Integrity": "0YPdADnRiM3ax9sNoIXUaSOAMJlrQcP+iUscGTrk63Q=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Tasks.wasm", "FileLength": 2557, "LastWriteTime": "2025-07-30T00:47:58.178645+00:00"}, "twvab5dfwryAi71QJ7MCEExgsNv4xBjChLjihRquchg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vib5qa8r4k-ilymmvzuiv.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Thread#[.{fingerprint=ilymmvzuiv}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "onw2011k98", "Integrity": "1MURrv8eLydkRsiEMP+Q55aHGzU4MoE8corWo2WTucc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Thread.wasm", "FileLength": 2331, "LastWriteTime": "2025-07-30T00:47:58.1796656+00:00"}, "C+UZ78NI0NdkaF5uLrX3i+ic8h5kor5uhDQN3afz8GI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\5wt3gr3td0-xvyp5vwm4h.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.ThreadPool#[.{fingerprint=xvyp5vwm4h}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4ssoo08bf9", "Integrity": "TAAPSTOv40PM7sNhJ+B5Pa015m4Yh/Me6Vfl441BFoE=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.ThreadPool.wasm", "FileLength": 2256, "LastWriteTime": "2025-07-30T00:47:58.1571693+00:00"}, "rzQ0GP1OGJcbAZkDbTFtNysAD46nbew9R1EmmIrkSPg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\cmkkgtziw3-rivg4u5uzk.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading.Timer#[.{fingerprint=rivg4u5uzk}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "my05rq0sxl", "Integrity": "4sjvd+KPwHn9GmqrprhR5fNk2XDCCW5IbwnVqwoMOpA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.Timer.wasm", "FileLength": 2118, "LastWriteTime": "2025-07-30T00:47:58.0842764+00:00"}, "VAQjCaK7KtR7FT8c20a9x4uXKzaMyc6EbuWpS8StQb4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\b6ir8mm4me-bsa3odzz7r.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Threading#[.{fingerprint=bsa3odzz7r}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "29zxmtb4z2", "Integrity": "tko6PtlmS34Awi0JkBkZ36KVsSt3DrX/tTCwmTcbrD0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Threading.wasm", "FileLength": 14941, "LastWriteTime": "2025-07-30T00:47:58.0918079+00:00"}, "0EaCNo8dH5PxzAdbSdUGu24rNxpVkhQXM4/hwkzQN2Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\obzjqov25e-iyerwqdgfh.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions.Local#[.{fingerprint=iyerwqdgfh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jbvm2go1v9", "Integrity": "D4p4SmjZI7F+GYyvvRvyDmkabz2PiuUwOOUiNBU3EB4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.Local.wasm", "FileLength": 52486, "LastWriteTime": "2025-07-30T00:47:58.1019327+00:00"}, "c9I9OKRvLlvCaCFVKBhOtVW+ZxY+CbQObjVAdXpTYFs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0rr4rbaozu-naml9dcyig.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Transactions#[.{fingerprint=naml9dcyig}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "wsd3bv4oai", "Integrity": "YJAVFJtzPaOsM8sSCTJCTSxu5dgYQ+6Dh50WgQx5/3U=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Transactions.wasm", "FileLength": 2365, "LastWriteTime": "2025-07-30T00:47:58.1064321+00:00"}, "XxhgEfFgfFvlZgvb6kQ/IqngemMwR8HVWhCvn1c7JiE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\kesf47unkk-895t9zulx2.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.ValueTuple#[.{fingerprint=895t9zulx2}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "luruhba945", "Integrity": "g3oyyrtPoHT0GsBGteTpqPRg+nYhcxtix2Rpnx4nqq0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.ValueTuple.wasm", "FileLength": 2169, "LastWriteTime": "2025-07-30T00:47:58.108933+00:00"}, "eXHOJR/0hoV2RiWxL71I23QNobnRxdrP0WBGiQyvhhs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\arhbcefb36-mgg9pig5ol.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web.HttpUtility#[.{fingerprint=mgg9pig5ol}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j5wjtkl0xs", "Integrity": "eXmY6+kAT4WrBh6NDrv9jt4mpqGmBdPN4DrnyaxZKI0=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.HttpUtility.wasm", "FileLength": 10063, "LastWriteTime": "2025-07-30T00:47:58.1162653+00:00"}, "ssL7R3cE9v7bTtBd+ajH4aWuY/nkaLrmbPjP9NVXS5M=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hvbbsdf83s-kb7fjpryvy.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Web#[.{fingerprint=kb7fjpryvy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "au1tn6l6x2", "Integrity": "ks1M1O5P//tjdibAMkiwNzHl0UubqaU98+66p3PGbm8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Web.wasm", "FileLength": 2117, "LastWriteTime": "2025-07-30T00:47:58.1241536+00:00"}, "dFsaoZfzTQ9aCVujb7GCmAOHS4NrTwUccSU6admd2rg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\87bgyqw6dz-puqm3d1199.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Windows#[.{fingerprint=puqm3d1199}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "742ednc3so", "Integrity": "yZS4mZKO3A2Ag6uu8ehr3B8crGi6Aer3z7kQJm7t9rI=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Windows.wasm", "FileLength": 2270, "LastWriteTime": "2025-07-30T00:47:58.1266635+00:00"}, "xv4/3UHr+4tH7HWAT8vSpAuSrC260tYq+vzdlzqsppE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\85gndfcc32-tcm6y315ec.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Linq#[.{fingerprint=tcm6y315ec}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "epdrwuh90o", "Integrity": "IiY3L5RhPkwSWplVthaS2dC6jQt9sNy+blbxozNxbew=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Linq.wasm", "FileLength": 2210, "LastWriteTime": "2025-07-30T00:47:58.1291626+00:00"}, "c+lRODl09zZXYwFZQuJ3dfPw9sX9yF5lSoChtFe8w/A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\0fkkcf19tb-nguzyrd9vx.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.ReaderWriter#[.{fingerprint=nguzyrd9vx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "shr1khq5tj", "Integrity": "kGZB8ybh78ehWNzkbtZdSFT9Xl4zUEZjJdSKM6EvKtg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.ReaderWriter.wasm", "FileLength": 4028, "LastWriteTime": "2025-07-30T00:47:58.1436891+00:00"}, "vJD8YBiRdolk+dXaTHosMS53uT1cyhkexF474fnl7U0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3dejcbw833-0ub3375c5y.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.Serialization#[.{fingerprint=0ub3375c5y}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "412kkfvzfa", "Integrity": "SCf7IdO6qWJPS9ytSrch+TYr4n6WkVekrJ/dHxJ2moo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.Serialization.wasm", "FileLength": 2245, "LastWriteTime": "2025-07-30T00:47:58.1496453+00:00"}, "V4sMneHEzjdm4721OcmbeEhXnUeLwDT8IFkuAK99QX8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\18fitgdsbc-rjragxq3cy.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XDocument#[.{fingerprint=rjragxq3cy}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "a4ws3smhv6", "Integrity": "mgRA/3RlLT9IDheOSYmZ/4lUyyaaET1NPH4kP64xxXQ=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XDocument.wasm", "FileLength": 2392, "LastWriteTime": "2025-07-30T00:47:58.1546638+00:00"}, "pY0CHWJWXdagrLPi/Q4jjDA1RGx48uzKpkB/kP64e2s=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\fnrrmsgjbs-q0h1rtwxr6.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath.XDocument#[.{fingerprint=q0h1rtwxr6}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "whsdfbfbhi", "Integrity": "fAM5Tvl1dmKmhY16NDBN96BbrNQ22vySWza0WSN3vBM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.XDocument.wasm", "FileLength": 2476, "LastWriteTime": "2025-07-30T00:47:58.1571693+00:00"}, "pPviJ+mgeQhOi2alhX20mT8G1oVoluS0x2wXuE9ywcU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\hb5bfqxk1a-ghy2aao3pw.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XPath#[.{fingerprint=ghy2aao3pw}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m0xok6mopc", "Integrity": "Joks4dj9ikJFzAseYWTVD2a0/jpFBrRFqp5uZ1kWe8M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XPath.wasm", "FileLength": 2316, "LastWriteTime": "2025-07-30T00:47:58.1591587+00:00"}, "yV3NX4JVadhCoOsG4MbGZ6LnsKOvziafaUV7sD2lDDk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\k63dbku8mu-h1hxdwycbb.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlDocument#[.{fingerprint=h1hxdwycbb}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rk5ez50x8f", "Integrity": "u4oaRTiomXxU8RIMODTX4crlDKDt4RyXOLv5UmWipuc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlDocument.wasm", "FileLength": 2347, "LastWriteTime": "2025-07-30T00:47:58.160671+00:00"}, "hNMwZbtOftX1+PxlpNuCL/aWWkSvU6Q0L4CNhtGkBCA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\souvgkjmmo-1z2utc94ww.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml.XmlSerializer#[.{fingerprint=1z2utc94ww}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "funerw60ai", "Integrity": "ybRwTxAYVLTlJAjq2I4+1FK4N1JgloSZLatDpFHasLU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.XmlSerializer.wasm", "FileLength": 2856, "LastWriteTime": "2025-07-30T00:47:58.1581633+00:00"}, "SWDkxjH26xAtAKwhEGg1jycU80+nsMcRue6rrFLI3zI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\qtyrrnv52a-8bxt02qawp.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Xml#[.{fingerprint=8bxt02qawp}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uqfztlkk95", "Integrity": "itQ43ubHnZLnf2xz3DABQbJbHZtDI0d2NS5lVM/ugcs=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Xml.wasm", "FileLength": 4239, "LastWriteTime": "2025-07-30T00:47:58.0852915+00:00"}, "lZ/P3P/GSMTIz6J6MwwpMSOH/Qup48d044xJWhcR3PE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\r1kywlspoz-549ex8tazx.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System#[.{fingerprint=549ex8tazx}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o1ik8auktq", "Integrity": "pBfkfaSm0CassVISvVhyG60mY2Je8iwFofLDnQvUfps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.wasm", "FileLength": 11852, "LastWriteTime": "2025-07-30T00:47:58.0909404+00:00"}, "TikNx2S9bbDTNN04fYeY0D3aW7F6JGSU36bVVFkh2Ys=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\ccp4ovb4zb-pg3o6xzigg.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/WindowsBase#[.{fingerprint=pg3o6xzigg}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j5jkzvue41", "Integrity": "99zMSlWZg21H41cmCfq8em8Mw3+H0fFXjQBPZu498y4=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\WindowsBase.wasm", "FileLength": 2510, "LastWriteTime": "2025-07-30T00:47:58.0953064+00:00"}, "3royt2sy6TXFXP5jyvCN27/9JOtoe7NQonFIZoSOOB0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\xds2pmn1lq-jl2pswegra.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/mscorlib#[.{fingerprint=jl2pswegra}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6nrt<PERSON><PERSON><PERSON>", "Integrity": "MhUYte4/6u077MQ072zqmTt8eNTHtXCQs+k+sw7TMps=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\mscorlib.wasm", "FileLength": 14888, "LastWriteTime": "2025-07-30T00:47:58.1008452+00:00"}, "smbdyfDLZ/M6gI5KzvHcqfkAV/pChadPEPXQ3l9PQTQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\egjpmt8hs2-uriri6ambh.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/netstandard#[.{fingerprint=uriri6ambh}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ket095f0zs", "Integrity": "JJ7+PXJY4MLTQoaCaRbsllc40ETJSrlEBvw2iEl7ZXk=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\netstandard.wasm", "FileLength": 26235, "LastWriteTime": "2025-07-30T00:47:58.1064321+00:00"}, "0NWAhY9iZCssHAHpL0LimdDCo5w/2vK9IRMlBgeM9ww=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\mncnn10p93-s7q7c40kwm.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/System.Private.CoreLib#[.{fingerprint=s7q7c40kwm}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9fuwz7cgrp", "Integrity": "BaGaOb51VGRlWecCCXJGRY/PKn2ZreJNiueIHehKleA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\System.Private.CoreLib.wasm", "FileLength": 1533173, "LastWriteTime": "2025-07-30T00:47:58.2234428+00:00"}, "WKmbK8F/+rovGiw7YzfhlFE/TEYpki8XfxSKpyWpT6w=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\8l7dii7j7b-cixy86cf2d.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet#[.{fingerprint=cixy86cf2d}]?.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "arkj3nb0x3", "Integrity": "1j4iutT90/YyrmF2rEqiunNCy8czlmHygY8WOoTEXEU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js", "FileLength": 12785, "LastWriteTime": "2025-07-30T00:47:58.226749+00:00"}, "uKnqG6Fb7G0dPVKsHmqPqlnX92LlEsIaTkYirWWDVN4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\d70sqkhiet-4bdu6o25zl.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.js#[.{fingerprint=4bdu6o25zl}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9btlwayiya", "Integrity": "pa94h4m3E4cay9zB7fC0lyRBP3Dcc6Kf336Su4BcrUo=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.js.map", "FileLength": 21303, "LastWriteTime": "2025-07-30T00:47:58.160671+00:00"}, "etJ9ng7U6wpUCzHAIz1CxZhlumxHocQObHpREdKUby4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\vz6rfjyj3j-st0ovrfdhi.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=st0ovrfdhi}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ep9501zdw5", "Integrity": "YtSKDJBw3kxivCjjdFSHyR+UWZUWKfp1t63xXsEM69I=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.js", "FileLength": 35024, "LastWriteTime": "2025-07-30T00:47:58.1662281+00:00"}, "Yuae2n9XsoLxK2XeRgN2N/7q/9WrXEEQJRxdUEEmtAc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\oxaxr1tg38-67rumul467.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.native#[.{fingerprint=67rumul467}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2swkhljzg", "Integrity": "Id66To3oeBJgesIYLo7TdKFzz3hEiEPkcLWU6J7f+aU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.native.wasm", "FileLength": 1199086, "LastWriteTime": "2025-07-30T00:47:58.2629007+00:00"}, "ec1U8oIacOeWfgeZechyqn56Uc7UxIUc7GJmEkSMmjo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\inx0plzxpx-593bvuk5yc.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime#[.{fingerprint=593bvuk5yc}]!.js.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "16rkymq4qj", "Integrity": "ieB2QE3S86I7BNNsbN0rwyyMAFywDMYcem1PUBpt/fA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js", "FileLength": 56235, "LastWriteTime": "2025-07-30T00:47:58.2691126+00:00"}, "061EZAlTZel2WH6o3Lx8lqSqSmBr9vGtWhGjIj/wMAA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\2czyr63cj3-egg93qn8th.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/dotnet.runtime.js#[.{fingerprint=egg93qn8th}]?.map.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "dnlddopvjs", "Integrity": "J3Q0xg5XDcfIHpG8tPqfzZ3sLgb+ru1deQuYXiTEJjU=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\dotnet.runtime.js.map", "FileLength": 88605, "LastWriteTime": "2025-07-30T00:47:58.2802117+00:00"}, "Ia11CUdzmWxjdmoJ+u9Wi8NwAmfZuqnMoS/n3LodQKc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\7jmkwetwhs-tjcz0u77k5.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_CJK#[.{fingerprint=tjcz0u77k5}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ffu5aujli6", "Integrity": "Sxt3k51yp5RyINpi7a/YQNnTWexafADiNQYBnBjQrYc=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_CJK.dat", "FileLength": 359724, "LastWriteTime": "2025-07-30T00:47:58.3123362+00:00"}, "mr/sGERTEjeetdnJjrCuSHrAanaCTS61wZGC2Av0qeU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\v6bsc6b9r9-tptq2av103.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_EFIGS#[.{fingerprint=tptq2av103}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kcp4n5gllp", "Integrity": "rLsEn/DgWXf7nRK2qegv2ARpYrcwixMOaXOoFcVmgjg=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_EFIGS.dat", "FileLength": 220055, "LastWriteTime": "2025-07-30T00:47:58.3282377+00:00"}, "JFwX59dlel13A1JM6k7n1TG0vB8Sz3m4yJUE8a0NN5Y=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\3ln5kotp08-lfu7j35m59.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/icudt_no_CJK#[.{fingerprint=lfu7j35m59}]!.dat.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "m3twp0zama", "Integrity": "UsST+ZWYFgogrz0pZB/4gGQWboPgRkurfdpi/0/ENCA=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\icudt_no_CJK.dat", "FileLength": 347023, "LastWriteTime": "2025-07-30T00:47:58.3486369+00:00"}, "UK520VmyojR0ahUVc1cre4pRg5z+r6EOmEh8MTs8RBQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\27199i5eju-s1mctel4vr.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/SinterBlendingSystem#[.{fingerprint=s1mctel4vr}]!.wasm.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\SinterBlendingSystem.wasm", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "piyg41d56c", "Integrity": "P7UBJVwfwPn8+H/HkMpXKKfCo3g5m3IFoGUsyoaDtHM=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\SinterBlendingSystem.wasm", "FileLength": 57094, "LastWriteTime": "2025-07-30T00:47:58.3529167+00:00"}, "kf2KwUrocV88E9anErSOG4cJwZrRVZF5LI8JMnA3DcE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\09i0t6iq8d-ng2olbsmcy.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/SinterBlendingSystem#[.{fingerprint=ng2olbsmcy}]!.pdb.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\SinterBlendingSystem.pdb", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "3vndm1u6ak", "Integrity": "D5SrrZCBhd9GZdSo46YRP1su/I2NXPyI90DXjwmVRP8=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\SinterBlendingSystem.pdb", "FileLength": 62193, "LastWriteTime": "2025-07-30T00:47:58.0893058+00:00"}, "Ew17zF6QE85LkyjI3TclZzukA5f8Tqek6ti3hJMITzY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\i4s14fy027-u1tqr8imda.gz", "SourceId": "SinterBlendingSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "/", "RelativePath": "_framework/blazor.boot.json.gz", "AssetKind": "Build", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "g<PERSON><PERSON><PERSON><PERSON>w", "Integrity": "1KxzTLXZc9sml5AQp5TznYeieqDR2E8mQJlarzgtC+M=", "CopyToOutputDirectory": "PreserveNewest", "CopyToPublishDirectory": "Never", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\bin\\Debug\\net9.0\\wwwroot\\_framework\\blazor.boot.json", "FileLength": 12499, "LastWriteTime": "2025-07-30T00:48:12.886316+00:00"}}, "CachedCopyCandidates": {}}