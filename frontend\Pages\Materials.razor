@page "/materials"
@inject IMaterialService MaterialService
@inject IStateService StateService
@inject INotificationService NotificationService
@inject IJSRuntime JSRuntime

<PageTitle>原料参数编辑</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
    <!-- 页面标题和操作栏 -->
    <MudGrid>
        <MudItem xs="12">
            <MudPaper Class="pa-4 mb-4" Elevation="2">
                <MudGrid AlignItems="Center">
                    <MudItem xs="6">
                        <MudText Typo="Typo.h5" Color="Color.Primary">
                            <MudIcon Icon="Icons.Material.Filled.Edit" Class="mr-2" />
                            原料参数编辑
                        </MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-1">
                            管理烧结配料原料的化学成分、价格和配比范围参数
                        </MudText>
                    </MudItem>
                    <MudItem xs="6" Class="text-right">
                        <MudButtonGroup Variant="Variant.Filled" Size="Size.Small">
                            <MudButton StartIcon="Icons.Material.Filled.Add" Color="Color.Primary" OnClick="AddNewMaterial">
                                新增原料
                            </MudButton>
                            <MudButton StartIcon="Icons.Material.Filled.Upload" Color="Color.Secondary" OnClick="ImportFromExcel">
                                导入Excel
                            </MudButton>
                            <MudButton StartIcon="Icons.Material.Filled.Download" Color="Color.Success" OnClick="ExportToExcel">
                                导出Excel
                            </MudButton>
                            <MudButton StartIcon="Icons.Material.Filled.Save" Color="Color.Warning" OnClick="SaveAllChanges" Disabled="!StateService.HasUnsavedChanges">
                                保存全部
                            </MudButton>
                        </MudButtonGroup>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- 12列栅格布局：左侧表格7列，右侧编辑5列 -->
    <MudGrid>
        <!-- 左侧：原料列表表格 (7列) -->
        <MudItem xs="7">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3">
                    <MudIcon Icon="Icons.Material.Filled.TableChart" Class="mr-2" />
                    原料列表 (共 @materials.Count 种)
                </MudText>
                
                <!-- 表格工具栏 -->
                <MudGrid Class="mb-3" AlignItems="Center">
                    <MudItem xs="4">
                        <MudTextField @bind-Value="searchText" 
                                    Placeholder="搜索原料名称或编号..." 
                                    Adornment="Adornment.Start" 
                                    AdornmentIcon="Icons.Material.Filled.Search"
                                    Immediate="true"
                                    OnAdornmentClick="SearchMaterials" />
                    </MudItem>
                    <MudItem xs="4">
                        <MudSelect @bind-Value="filterSelected" Label="筛选状态" Dense="true">
                            <MudSelectItem Value="@("all")">全部原料</MudSelectItem>
                            <MudSelectItem Value="@("selected")">已选择</MudSelectItem>
                            <MudSelectItem Value="@("unselected")">未选择</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="4" Class="text-right">
                        <MudButton StartIcon="Icons.Material.Filled.SelectAll" 
                                 Size="Size.Small" 
                                 Variant="Variant.Outlined"
                                 OnClick="() => SelectAllMaterials(true)">
                            全选
                        </MudButton>
                        <MudButton StartIcon="Icons.Material.Filled.DeselectAll" 
                                 Size="Size.Small" 
                                 Variant="Variant.Outlined"
                                 OnClick="() => SelectAllMaterials(false)"
                                 Class="ml-2">
                            全不选
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <!-- 原料数据表格 -->
                <MudTable Items="@filteredMaterials" 
                         Dense="true" 
                         Hover="true" 
                         Striped="true" 
                         FixedHeader="true"
                         Height="600px"
                         @bind-SelectedItem="selectedMaterial"
                         T="MaterialModel">
                    <HeaderContent>
                        <MudTh Style="width: 60px;">选择</MudTh>
                        <MudTh Style="width: 80px;">编号</MudTh>
                        <MudTh Style="width: 120px;">原料名称</MudTh>
                        <MudTh Style="width: 80px;">TFe(%)</MudTh>
                        <MudTh Style="width: 80px;">CaO(%)</MudTh>
                        <MudTh Style="width: 80px;">SiO2(%)</MudTh>
                        <MudTh Style="width: 80px;">MgO(%)</MudTh>
                        <MudTh Style="width: 80px;">Al2O3(%)</MudTh>
                        <MudTh Style="width: 80px;">水分(%)</MudTh>
                        <MudTh Style="width: 80px;">烧损(%)</MudTh>
                        <MudTh Style="width: 100px;">价格(元/吨)</MudTh>
                        <MudTh Style="width: 100px;">配比范围(%)</MudTh>
                        <MudTh Style="width: 80px;">操作</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <MudCheckBox @bind-Checked="context.IsSelected" 
                                       Color="Color.Primary" 
                                       Size="Size.Small"
                                       T="bool" />
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2">@context.MaterialCode</MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2" Style="@(context.HasMoistureWarning ? "color: #F53F3F;" : "")">
                                @context.MaterialName
                                @if (context.HasMoistureWarning)
                                {
                                    <MudIcon Icon="Icons.Material.Filled.Warning" Size="Size.Small" Color="Color.Error" />
                                }
                            </MudText>
                        </MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.WetTFe.ToString("F2")</MudText></MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.WetCaO.ToString("F2")</MudText></MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.WetSiO2.ToString("F2")</MudText></MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.WetMgO.ToString("F2")</MudText></MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.WetAl2O3.ToString("F2")</MudText></MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2" Style="@(context.HasMoistureWarning ? "color: #F53F3F; font-weight: bold;" : "")">
                                @context.Moisture.ToString("F1")
                            </MudText>
                        </MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.LossOnIgnition.ToString("F1")</MudText></MudTd>
                        <MudTd><MudText Typo="Typo.body2">@context.Price.ToString("F2")</MudText></MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2" Style="@(context.HasRatioError ? "color: #F53F3F;" : "")">
                                @context.MinRatio.ToString("F1") - @context.MaxRatio.ToString("F1")
                                @if (context.HasRatioError)
                                {
                                    <MudIcon Icon="Icons.Material.Filled.Error" Size="Size.Small" Color="Color.Error" />
                                }
                            </MudText>
                        </MudTd>
                        <MudTd>
                            <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                                <MudIconButton Icon="Icons.Material.Filled.Edit" 
                                             Color="Color.Primary" 
                                             Size="Size.Small"
                                             OnClick="() => EditMaterial(context)" />
                                <MudIconButton Icon="Icons.Material.Filled.Delete" 
                                             Color="Color.Error" 
                                             Size="Size.Small"
                                             OnClick="() => DeleteMaterial(context)" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudPaper>
        </MudItem>

        <!-- 右侧：编辑面板 (5列) -->
        <MudItem xs="5">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3">
                    <MudIcon Icon="Icons.Material.Filled.Edit" Class="mr-2" />
                    @(editingMaterial?.Id > 0 ? "编辑原料" : "新增原料")
                </MudText>
                
                @if (editingMaterial != null)
                {
                    <EditForm Model="editingMaterial" OnValidSubmit="SaveMaterial">
                        <DataAnnotationsValidator />
                        
                        <!-- 基本信息 -->
                        <MudCard Class="mb-4">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">基本信息</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudTextField @bind-Value="editingMaterial.MaterialCode" 
                                                    Label="原料编号" 
                                                    Required="true"
                                                    For="@(() => editingMaterial.MaterialCode)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudTextField @bind-Value="editingMaterial.MaterialName" 
                                                    Label="原料名称" 
                                                    Required="true"
                                                    For="@(() => editingMaterial.MaterialName)" />
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>

                        <!-- 湿基化学成分 -->
                        <MudCard Class="mb-4">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">湿基化学成分 (%)</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.WetTFe" 
                                                       Label="TFe含量" 
                                                       Format="F2"
                                                       Min="0" Max="100"
                                                       For="@(() => editingMaterial.WetTFe)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.WetCaO" 
                                                       Label="CaO含量" 
                                                       Format="F2"
                                                       Min="0" Max="100"
                                                       For="@(() => editingMaterial.WetCaO)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.WetSiO2" 
                                                       Label="SiO2含量" 
                                                       Format="F2"
                                                       Min="0" Max="100"
                                                       For="@(() => editingMaterial.WetSiO2)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.WetMgO" 
                                                       Label="MgO含量" 
                                                       Format="F2"
                                                       Min="0" Max="100"
                                                       For="@(() => editingMaterial.WetMgO)" />
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudNumericField @bind-Value="editingMaterial.WetAl2O3" 
                                                       Label="Al2O3含量" 
                                                       Format="F2"
                                                       Min="0" Max="100"
                                                       For="@(() => editingMaterial.WetAl2O3)" />
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>

                        <!-- 水分和烧损 -->
                        <MudCard Class="mb-4">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">水分和烧损</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.Moisture" 
                                                       Label="水分 (%)" 
                                                       Format="F1"
                                                       Min="0" Max="20"
                                                       HelperText="@(editingMaterial.HasMoistureWarning ? "警告：水分过高" : "")"
                                                       HelperTextOnFocus="true"
                                                       For="@(() => editingMaterial.Moisture)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.LossOnIgnition" 
                                                       Label="烧损 (%)" 
                                                       Format="F1"
                                                       Min="-10" Max="100"
                                                       For="@(() => editingMaterial.LossOnIgnition)" />
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>

                        <!-- 价格和配比范围 -->
                        <MudCard Class="mb-4">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">价格和配比范围</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12">
                                        <MudNumericField @bind-Value="editingMaterial.Price" 
                                                       Label="价格 (元/吨)" 
                                                       Format="F2"
                                                       Min="0"
                                                       For="@(() => editingMaterial.Price)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.MinRatio" 
                                                       Label="配比下限 (%)" 
                                                       Format="F1"
                                                       Min="0" Max="100"
                                                       For="@(() => editingMaterial.MinRatio)" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudNumericField @bind-Value="editingMaterial.MaxRatio" 
                                                       Label="配比上限 (%)" 
                                                       Format="F1"
                                                       Min="0" Max="100"
                                                       HelperText="@(editingMaterial.HasRatioError ? "错误：上限不能小于下限" : "")"
                                                       HelperTextOnFocus="true"
                                                       For="@(() => editingMaterial.MaxRatio)" />
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>

                        <!-- 干基成分预览 -->
                        <MudCard Class="mb-4">
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">干基成分预览 (%)</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="6">
                                        <MudTextField Value="@editingMaterial.DryTFe.ToString("F2")" 
                                                    Label="干基TFe" 
                                                    ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudTextField Value="@editingMaterial.DryCaO.ToString("F2")" 
                                                    Label="干基CaO" 
                                                    ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudTextField Value="@editingMaterial.DrySiO2.ToString("F2")" 
                                                    Label="干基SiO2" 
                                                    ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="6">
                                        <MudTextField Value="@editingMaterial.DryMgO.ToString("F2")" 
                                                    Label="干基MgO" 
                                                    ReadOnly="true" />
                                    </MudItem>
                                    <MudItem xs="12">
                                        <MudTextField Value="@editingMaterial.DryAl2O3.ToString("F2")" 
                                                    Label="干基Al2O3" 
                                                    ReadOnly="true" />
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>

                        <!-- 操作按钮 -->
                        <MudCardActions>
                            <MudButton ButtonType="ButtonType.Submit" 
                                     Variant="Variant.Filled" 
                                     Color="Color.Primary"
                                     StartIcon="Icons.Material.Filled.Save">
                                保存
                            </MudButton>
                            <MudButton Variant="Variant.Outlined" 
                                     Color="Color.Secondary"
                                     OnClick="CancelEdit"
                                     StartIcon="Icons.Material.Filled.Cancel">
                                取消
                            </MudButton>
                        </MudCardActions>
                        
                        <ValidationSummary />
                    </EditForm>
                }
                else
                {
                    <MudAlert Severity="Severity.Info">
                        <MudText>请从左侧表格选择要编辑的原料，或点击"新增原料"按钮添加新原料。</MudText>
                    </MudAlert>
                }
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private List<MaterialModel> materials = new();
    private List<MaterialModel> filteredMaterials = new();
    private MaterialModel? selectedMaterial;
    private MaterialModel? editingMaterial;
    private string searchText = "";
    private string filterSelected = "all";

    protected override async Task OnInitializedAsync()
    {
        await LoadMaterials();
        StateService.OnStateChanged += StateHasChanged;
    }

    private async Task LoadMaterials()
    {
        materials = await MaterialService.GetMaterialsAsync();
        StateService.Materials = materials;
        FilterMaterials();
    }

    private void FilterMaterials()
    {
        filteredMaterials = materials.Where(m =>
        {
            // 搜索过滤
            bool matchesSearch = string.IsNullOrEmpty(searchText) ||
                               m.MaterialName.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                               m.MaterialCode.Contains(searchText, StringComparison.OrdinalIgnoreCase);

            // 状态过滤
            bool matchesFilter = filterSelected switch
            {
                "selected" => m.IsSelected,
                "unselected" => !m.IsSelected,
                _ => true
            };

            return matchesSearch && matchesFilter;
        }).ToList();
    }

    private void SearchMaterials()
    {
        FilterMaterials();
    }

    private void AddNewMaterial()
    {
        editingMaterial = new MaterialModel
        {
            Id = 0,
            MaterialCode = $"M{materials.Count + 1:000}",
            MaterialName = "",
            IsSelected = false
        };
    }

    private void EditMaterial(MaterialModel material)
    {
        editingMaterial = new MaterialModel
        {
            Id = material.Id,
            MaterialCode = material.MaterialCode,
            MaterialName = material.MaterialName,
            WetTFe = material.WetTFe,
            WetCaO = material.WetCaO,
            WetSiO2 = material.WetSiO2,
            WetMgO = material.WetMgO,
            WetAl2O3 = material.WetAl2O3,
            Moisture = material.Moisture,
            LossOnIgnition = material.LossOnIgnition,
            Price = material.Price,
            MinRatio = material.MinRatio,
            MaxRatio = material.MaxRatio,
            IsSelected = material.IsSelected
        };
    }

    private async Task SaveMaterial()
    {
        if (editingMaterial != null && editingMaterial.IsValid())
        {
            var success = await MaterialService.SaveMaterialAsync(editingMaterial);
            if (success)
            {
                await LoadMaterials();
                editingMaterial = null;
                StateService.NotifyStateChanged();
            }
        }
    }

    private void CancelEdit()
    {
        editingMaterial = null;
    }

    private async Task DeleteMaterial(MaterialModel material)
    {
        var success = await MaterialService.DeleteMaterialAsync(material.Id);
        if (success)
        {
            await LoadMaterials();
            if (editingMaterial?.Id == material.Id)
            {
                editingMaterial = null;
            }
            StateService.NotifyStateChanged();
        }
    }

    private void SelectAllMaterials(bool selected)
    {
        foreach (var material in filteredMaterials)
        {
            material.IsSelected = selected;
        }
        StateService.NotifyStateChanged();
    }

    private async Task SaveAllChanges()
    {
        var success = await MaterialService.SaveMaterialsAsync(materials);
        if (success)
        {
            StateService.HasUnsavedChanges = false;
            StateService.NotifyStateChanged();
        }
    }

    private void ImportFromExcel()
    {
        // TODO: 实现Excel导入功能
        NotificationService.ShowInfo("Excel导入功能开发中...");
    }

    private void ExportToExcel()
    {
        // TODO: 实现Excel导出功能
        NotificationService.ShowInfo("Excel导出功能开发中...");
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
