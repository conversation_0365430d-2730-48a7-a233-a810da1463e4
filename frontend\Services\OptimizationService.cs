using System.Net.Http.Json;
using System.Text.Json;
using SinterBlendingSystem.Models;

namespace SinterBlendingSystem.Services;

public class OptimizationService : IOptimizationService
{
    private readonly HttpClient _httpClient;
    private readonly INotificationService _notification;

    public OptimizationService(HttpClient httpClient, INotificationService notification)
    {
        _httpClient = httpClient;
        _notification = notification;
    }

    public async Task<OptimizationResult> OptimizeAsync(
        List<MaterialModel> materials,
        OptimizationTarget target,
        ConstraintRanges constraints,
        OptimizationType optimizationType,
        bool multiSolution = false)
    {
        try
        {
            _notification.ShowProgress("正在计算最优方案...", 0);

            // 构建请求数据
            var requestData = new
            {
                optimize_type = optimizationType == OptimizationType.CostOptimal ? "cost" : "quality",
                multi_solution = multiSolution,
                raw_materials = materials.Where(m => m.IsSelected).Select(m => new
                {
                    name = m.MaterialName,
                    tfe = (double)m.WetTFe,
                    cao = (double)m.WetCaO,
                    sio2 = (double)m.WetSiO2,
                    mgo = (double)m.WetMgO,
                    al2o3 = (double)m.WetAl2O3,
                    h2o = (double)m.Moisture,
                    ig = (double)m.LossOnIgnition,
                    price = (double)m.Price,
                    min_ratio = (double)m.MinRatio,
                    max_ratio = (double)m.MaxRatio
                }).ToArray(),
                target = new
                {
                    tfe_target = (double)target.TFeTarget,
                    ro_target = (double)target.RoTarget,
                    mgo_target = (double)target.MgOTarget,
                    al2o3_target = (double)target.Al2O3Target
                },
                constraints = new
                {
                    tfe_range = new[] { (double)constraints.TFeMin, (double)constraints.TFeMax },
                    ro_range = new[] { (double)constraints.RoMin, (double)constraints.RoMax },
                    mgo_range = new[] { (double)constraints.MgOMin, (double)constraints.MgOMax },
                    al2o3_range = new[] { (double)constraints.Al2O3Min, (double)constraints.Al2O3Max },
                    cost_range = new[] { (double)constraints.CostMin, (double)constraints.CostMax }
                }
            };

            _notification.ShowProgress("正在计算最优方案...", 30);

            // 发送请求
            var response = await _httpClient.PostAsJsonAsync("api/solve", requestData);
            
            _notification.ShowProgress("正在计算最优方案...", 70);

            if (response.IsSuccessStatusCode)
            {
                var jsonResponse = await response.Content.ReadAsStringAsync();
                var apiResult = JsonSerializer.Deserialize<JsonElement>(jsonResponse);

                _notification.ShowProgress("正在计算最优方案...", 90);

                if (apiResult.GetProperty("success").GetBoolean())
                {
                    var result = new OptimizationResult
                    {
                        Success = true,
                        OptimizationType = optimizationType,
                        OptimalRatios = new Dictionary<string, decimal>(),
                        Properties = new SinterProperties(),
                        Iterations = apiResult.GetProperty("iterations").GetInt32(),
                        Message = apiResult.GetProperty("message").GetString() ?? "优化成功",
                        Timestamp = DateTime.Now
                    };

                    // 解析配比结果
                    if (apiResult.TryGetProperty("optimal_ratios", out var ratios))
                    {
                        foreach (var ratio in ratios.EnumerateObject())
                        {
                            result.OptimalRatios[ratio.Name] = (decimal)ratio.Value.GetDouble();
                        }
                    }

                    // 解析烧结矿性质
                    if (apiResult.TryGetProperty("sinter_properties", out var properties))
                    {
                        result.Properties.TFe = (decimal)properties.GetProperty("TFe").GetDouble();
                        result.Properties.R = (decimal)properties.GetProperty("R").GetDouble();
                        result.Properties.MgO = (decimal)properties.GetProperty("MgO").GetDouble();
                        result.Properties.Al2O3 = (decimal)properties.GetProperty("Al2O3").GetDouble();
                        result.Properties.Cost = (decimal)properties.GetProperty("Cost").GetDouble();
                    }

                    result.UnitCost = (decimal)apiResult.GetProperty("unit_cost").GetDouble();
                    result.ObjectiveValue = (decimal)apiResult.GetProperty("objective_value").GetDouble();

                    // 解析备选方案
                    if (apiResult.TryGetProperty("alternative_solution", out var altSolution))
                    {
                        result.AlternativeSolution = new OptimizationResult
                        {
                            Success = true,
                            OptimizationType = altSolution.GetProperty("optimization_type").GetString() == "cost" 
                                ? OptimizationType.CostOptimal : OptimizationType.QualityOptimal,
                            OptimalRatios = new Dictionary<string, decimal>(),
                            Properties = new SinterProperties(),
                            UnitCost = (decimal)altSolution.GetProperty("unit_cost").GetDouble(),
                            ObjectiveValue = (decimal)altSolution.GetProperty("objective_value").GetDouble()
                        };

                        // 解析备选方案配比
                        if (altSolution.TryGetProperty("optimal_ratios", out var altRatios))
                        {
                            foreach (var ratio in altRatios.EnumerateObject())
                            {
                                result.AlternativeSolution.OptimalRatios[ratio.Name] = (decimal)ratio.Value.GetDouble();
                            }
                        }

                        // 解析备选方案性质
                        if (altSolution.TryGetProperty("sinter_properties", out var altProperties))
                        {
                            result.AlternativeSolution.Properties.TFe = (decimal)altProperties.GetProperty("TFe").GetDouble();
                            result.AlternativeSolution.Properties.R = (decimal)altProperties.GetProperty("R").GetDouble();
                            result.AlternativeSolution.Properties.MgO = (decimal)altProperties.GetProperty("MgO").GetDouble();
                            result.AlternativeSolution.Properties.Al2O3 = (decimal)altProperties.GetProperty("Al2O3").GetDouble();
                            result.AlternativeSolution.Properties.Cost = (decimal)altProperties.GetProperty("Cost").GetDouble();
                        }
                    }

                    _notification.HideProgress();
                    _notification.ShowSuccess($"优化计算完成！成本: {result.UnitCost:F2}元/吨");
                    return result;
                }
                else
                {
                    var errorMessage = apiResult.GetProperty("error").GetString() ?? "优化失败";
                    _notification.HideProgress();
                    _notification.ShowError($"优化失败: {errorMessage}");
                    
                    return new OptimizationResult
                    {
                        Success = false,
                        Message = errorMessage,
                        OptimizationType = optimizationType
                    };
                }
            }
            else
            {
                _notification.HideProgress();
                _notification.ShowError($"服务器响应错误: {response.StatusCode}");
                
                return new OptimizationResult
                {
                    Success = false,
                    Message = $"服务器响应错误: {response.StatusCode}",
                    OptimizationType = optimizationType
                };
            }
        }
        catch (Exception ex)
        {
            _notification.HideProgress();
            _notification.ShowError($"优化计算异常: {ex.Message}");
            
            return new OptimizationResult
            {
                Success = false,
                Message = $"优化计算异常: {ex.Message}",
                OptimizationType = optimizationType
            };
        }
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("health");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }
}
