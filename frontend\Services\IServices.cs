using SinterBlendingSystem.Models;

namespace SinterBlendingSystem.Services;

/// <summary>
/// 原料数据服务接口
/// </summary>
public interface IMaterialService
{
    Task<List<MaterialModel>> GetMaterialsAsync();
    Task<MaterialModel?> GetMaterialAsync(int id);
    Task<bool> SaveMaterialAsync(MaterialModel material);
    Task<bool> DeleteMaterialAsync(int id);
    Task<bool> SaveMaterialsAsync(List<MaterialModel> materials);
    Task<bool> ImportMaterialsFromExcelAsync(Stream excelStream);
    Task<byte[]> ExportMaterialsToExcelAsync();
    Task<byte[]> GetExcelTemplateAsync();
}

/// <summary>
/// 优化算法服务接口
/// </summary>
public interface IOptimizationService
{
    Task<OptimizationResult> OptimizeAsync(
        List<MaterialModel> materials,
        OptimizationTarget target,
        ConstraintRanges constraints,
        OptimizationType optimizationType,
        bool multiSolution = false);
    
    Task<bool> TestConnectionAsync();
}

/// <summary>
/// 状态管理服务接口
/// </summary>
public interface IStateService
{
    // 原料数据状态
    List<MaterialModel> Materials { get; set; }
    List<MaterialModel> SelectedMaterials { get; }
    bool HasUnsavedChanges { get; set; }
    
    // 优化参数状态
    OptimizationTarget Target { get; set; }
    ConstraintRanges Constraints { get; set; }
    OptimizationType CurrentOptimizationType { get; set; }
    
    // 结果状态
    OptimizationResult? LastResult { get; set; }
    List<OptimizationResult> ResultHistory { get; set; }
    
    // 事件
    event Action? OnStateChanged;
    
    // 方法
    void NotifyStateChanged();
    void SelectMaterial(int materialId, bool selected);
    void SelectAllMaterials(bool selected);
    decimal GetTotalRatio();
    bool ValidateConstraints();
    void SaveToLocalStorage();
    Task LoadFromLocalStorageAsync();
}

/// <summary>
/// 本地存储服务接口
/// </summary>
public interface ILocalStorageService
{
    Task SetItemAsync<T>(string key, T value);
    Task<T?> GetItemAsync<T>(string key);
    Task RemoveItemAsync(string key);
    Task ClearAsync();
}

/// <summary>
/// 通知服务接口
/// </summary>
public interface INotificationService
{
    void ShowSuccess(string message);
    void ShowError(string message);
    void ShowWarning(string message);
    void ShowInfo(string message);
    void ShowProgress(string message, int percentage);
    void HideProgress();
}

/// <summary>
/// 文件服务接口
/// </summary>
public interface IFileService
{
    Task<byte[]> ReadFileAsync(Stream stream);
    Task DownloadFileAsync(byte[] data, string fileName, string contentType);
    Task<Stream?> OpenFileDialogAsync(string[] allowedExtensions);
}

/// <summary>
/// 图表服务接口
/// </summary>
public interface IChartService
{
    Task InitializeChartsAsync();
    Task RenderPieChartAsync(string containerId, Dictionary<string, decimal> data, string title);
    Task RenderRadarChartAsync(string containerId, Dictionary<string, decimal> targetValues, 
        Dictionary<string, decimal> actualValues, string title);
    Task RenderLineChartAsync(string containerId, List<decimal> costData, List<decimal> qualityData, string title);
    Task UpdateChartAsync(string containerId, object newData);
    Task DisposeChartAsync(string containerId);
}

/// <summary>
/// 验证服务接口
/// </summary>
public interface IValidationService
{
    ValidationResult ValidateMaterial(MaterialModel material);
    ValidationResult ValidateTarget(OptimizationTarget target);
    ValidationResult ValidateConstraints(ConstraintRanges constraints);
    ValidationResult ValidateMaterialList(List<MaterialModel> materials);
    bool IsRatioSumValid(List<MaterialModel> materials);
}

/// <summary>
/// 验证结果模型
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    
    public void AddError(string error) => Errors.Add(error);
    public void AddWarning(string warning) => Warnings.Add(warning);
    public bool HasErrors => Errors.Any();
    public bool HasWarnings => Warnings.Any();
}

/// <summary>
/// 应用配置模型
/// </summary>
public class AppConfig
{
    public string ApiBaseUrl { get; set; } = "http://localhost:5000";
    public int RequestTimeoutSeconds { get; set; } = 30;
    public int MaxRetryAttempts { get; set; } = 3;
    public bool EnableLocalStorage { get; set; } = true;
    public bool EnableAutoSave { get; set; } = true;
    public int AutoSaveIntervalSeconds { get; set; } = 300;
    public string DefaultLanguage { get; set; } = "zh-CN";
    public bool EnableDebugMode { get; set; } = false;
}
