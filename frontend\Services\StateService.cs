using Microsoft.JSInterop;
using MudBlazor;
using SinterBlendingSystem.Models;

namespace SinterBlendingSystem.Services;

public class StateService : IStateService
{
    private readonly ILocalStorageService _localStorage;

    public StateService(ILocalStorageService localStorage)
    {
        _localStorage = localStorage;
        Materials = new List<MaterialModel>();
        Target = new OptimizationTarget();
        Constraints = new ConstraintRanges();
        ResultHistory = new List<OptimizationResult>();
        CurrentOptimizationType = OptimizationType.CostOptimal;
    }

    // 原料数据状态
    public List<MaterialModel> Materials { get; set; }
    public List<MaterialModel> SelectedMaterials => Materials.Where(m => m.IsSelected).ToList();
    public bool HasUnsavedChanges { get; set; }

    // 优化参数状态
    public OptimizationTarget Target { get; set; }
    public ConstraintRanges Constraints { get; set; }
    public OptimizationType CurrentOptimizationType { get; set; }

    // 结果状态
    public OptimizationResult? LastResult { get; set; }
    public List<OptimizationResult> ResultHistory { get; set; }

    // 事件
    public event Action? OnStateChanged;

    // 方法
    public void NotifyStateChanged()
    {
        OnStateChanged?.Invoke();
        HasUnsavedChanges = true;
        SaveToLocalStorage();
    }

    public void SelectMaterial(int materialId, bool selected)
    {
        var material = Materials.FirstOrDefault(m => m.Id == materialId);
        if (material != null)
        {
            material.IsSelected = selected;
            NotifyStateChanged();
        }
    }

    public void SelectAllMaterials(bool selected)
    {
        foreach (var material in Materials)
        {
            material.IsSelected = selected;
        }
        NotifyStateChanged();
    }

    public decimal GetTotalRatio()
    {
        return SelectedMaterials.Sum(m => (m.MinRatio + m.MaxRatio) / 2);
    }

    public bool ValidateConstraints()
    {
        // 检查是否有选中的原料
        if (!SelectedMaterials.Any())
            return false;

        // 检查约束范围是否合理
        if (Constraints.TFeMin >= Constraints.TFeMax ||
            Constraints.RoMin >= Constraints.RoMax ||
            Constraints.MgOMin >= Constraints.MgOMax ||
            Constraints.Al2O3Min >= Constraints.Al2O3Max ||
            Constraints.CostMin >= Constraints.CostMax)
            return false;

        // 检查原料配比范围是否合理
        foreach (var material in SelectedMaterials)
        {
            if (material.MinRatio > material.MaxRatio)
                return false;
        }

        return true;
    }

    public void SaveToLocalStorage()
    {
        try
        {
            var state = new
            {
                Materials,
                Target,
                Constraints,
                CurrentOptimizationType,
                LastResult,
                ResultHistory = ResultHistory.TakeLast(10).ToList(), // 只保存最近10个结果
                SaveTime = DateTime.Now
            };

            _ = Task.Run(async () =>
            {
                await _localStorage.SetItemAsync("app_state", state);
            });
        }
        catch
        {
            // 忽略保存错误
        }
    }

    public async Task LoadFromLocalStorageAsync()
    {
        try
        {
            var state = await _localStorage.GetItemAsync<dynamic>("app_state");
            if (state != null)
            {
                // TODO: 实现状态恢复逻辑
                // 由于dynamic类型的复杂性，这里需要更详细的实现
            }
        }
        catch
        {
            // 忽略加载错误，使用默认状态
        }
    }
}

// 简单的本地存储服务实现
public class LocalStorageService : ILocalStorageService
{
    private readonly IJSRuntime _jsRuntime;

    public LocalStorageService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public async Task SetItemAsync<T>(string key, T value)
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(value);
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", key, json);
        }
        catch
        {
            // 忽略存储错误
        }
    }

    public async Task<T?> GetItemAsync<T>(string key)
    {
        try
        {
            var json = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", key);
            if (string.IsNullOrEmpty(json))
                return default;

            return System.Text.Json.JsonSerializer.Deserialize<T>(json);
        }
        catch
        {
            return default;
        }
    }

    public async Task RemoveItemAsync(string key)
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", key);
        }
        catch
        {
            // 忽略删除错误
        }
    }

    public async Task ClearAsync()
    {
        try
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.clear");
        }
        catch
        {
            // 忽略清除错误
        }
    }
}

// 通知服务实现
public class NotificationService : INotificationService
{
    private readonly ISnackbar _snackbar;

    public NotificationService(ISnackbar snackbar)
    {
        _snackbar = snackbar;
    }

    public void ShowSuccess(string message)
    {
        _snackbar.Add(message, Severity.Success);
    }

    public void ShowError(string message)
    {
        _snackbar.Add(message, Severity.Error);
    }

    public void ShowWarning(string message)
    {
        _snackbar.Add(message, Severity.Warning);
    }

    public void ShowInfo(string message)
    {
        _snackbar.Add(message, Severity.Info);
    }

    public void ShowProgress(string message, int percentage)
    {
        _snackbar.Add($"{message} ({percentage}%)", Severity.Info);
    }

    public void HideProgress()
    {
        // MudBlazor的Snackbar会自动隐藏
    }
}
