@page "/optimization/target"
@inject IStateService StateService
@inject INotificationService NotificationService
@inject NavigationManager Navigation

<PageTitle>目标与约束配置</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="pa-4">
    <!-- 页面标题 -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="8">
                <MudText Typo="Typo.h5" Color="Color.Primary">
                    <MudIcon Icon="Icons.Material.Filled.Target" Class="mr-2" />
                    目标与约束配置
                </MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mt-1">
                    配置烧结配料优化的目标参数和约束条件
                </MudText>
            </MudItem>
            <MudItem xs="4" Class="text-right">
                <MudChip T="string" Color="Color.Info" Size="Size.Small">
                    已选择 @StateService.SelectedMaterials.Count 种原料
                </MudChip>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 分步表单 -->
    <MudStepper @ref="stepper" Color="Color.Primary" Variant="Variant.Filled">
        
        <!-- 步骤1：优化目标选择 -->
        <MudStep Title="优化目标" Icon="Icons.Material.Filled.Flag">
            <ChildContent>
                <MudPaper Class="pa-6" Elevation="1">
                    <MudText Typo="Typo.h6" Class="mb-4">选择优化目标类型</MudText>
                    
                    <MudGrid>
                        <MudItem xs="6">
                            <MudCard Class="@GetOptimizationCardClass(OptimizationType.CostOptimal)" 
                                   Style="cursor: pointer; min-height: 200px;"
                                   @onclick="() => SelectOptimizationType(OptimizationType.CostOptimal)">
                                <MudCardContent Class="text-center pa-6">
                                    <MudIcon Icon="Icons.Material.Filled.AttachMoney" 
                                           Size="Size.Large" 
                                           Color="Color.Success" 
                                           Class="mb-3" />
                                    <MudText Typo="Typo.h5" Class="mb-2">成本最优</MudText>
                                    <MudText Typo="Typo.body1" Color="Color.Secondary">
                                        以最低成本为主要目标，在满足质量约束的前提下，寻找成本最低的配料方案
                                    </MudText>
                                    <MudDivider Class="my-3" />
                                    <MudText Typo="Typo.body2" Color="Color.Primary">
                                        • 优先考虑原料价格<br/>
                                        • 质量指标作为约束条件<br/>
                                        • 适用于成本敏感场景
                                    </MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                        
                        <MudItem xs="6">
                            <MudCard Class="@GetOptimizationCardClass(OptimizationType.QualityOptimal)" 
                                   Style="cursor: pointer; min-height: 200px;"
                                   @onclick="() => SelectOptimizationType(OptimizationType.QualityOptimal)">
                                <MudCardContent Class="text-center pa-6">
                                    <MudIcon Icon="Icons.Material.Filled.HighQuality" 
                                           Size="Size.Large" 
                                           Color="Color.Warning" 
                                           Class="mb-3" />
                                    <MudText Typo="Typo.h5" Class="mb-2">质量最优</MudText>
                                    <MudText Typo="Typo.body1" Color="Color.Secondary">
                                        以最佳质量为主要目标，在成本约束范围内，寻找质量指标最接近目标的配料方案
                                    </MudText>
                                    <MudDivider Class="my-3" />
                                    <MudText Typo="Typo.body2" Color="Color.Primary">
                                        • 优先考虑质量指标<br/>
                                        • 成本作为约束条件<br/>
                                        • 适用于质量要求严格场景
                                    </MudText>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    </MudGrid>
                    
                    @if (StateService.CurrentOptimizationType != OptimizationType.CostOptimal && 
                         StateService.CurrentOptimizationType != OptimizationType.QualityOptimal)
                    {
                        <MudAlert Severity="Severity.Warning" Class="mt-4">
                            请选择一种优化目标类型
                        </MudAlert>
                    }
                </MudPaper>
            </ChildContent>
        </MudStep>

        <!-- 步骤2：目标参数设置 -->
        <MudStep Title="目标参数" Icon="Icons.Material.Filled.Tune">
            <ChildContent>
                <MudPaper Class="pa-6" Elevation="1">
                    <MudText Typo="Typo.h6" Class="mb-4">设置烧结矿目标成分</MudText>
                    
                    <EditForm Model="StateService.Target" OnValidSubmit="SaveTargetParameters">
                        <DataAnnotationsValidator />
                        
                        <MudGrid>
                            <MudItem xs="6">
                                <MudCard Class="mb-4">
                                    <MudCardHeader>
                                        <CardHeaderContent>
                                            <MudText Typo="Typo.h6">主要成分目标</MudText>
                                        </CardHeaderContent>
                                    </MudCardHeader>
                                    <MudCardContent>
                                        <MudGrid>
                                            <MudItem xs="12">
                                                <MudNumericField T="decimal" @bind-Value="StateService.Target.TFeTarget"
                                                               Label="TFe目标值 (%)"
                                                               Format="F2"
                                                               Min="50m" Max="70m"
                                                               Step="0.1m"
                                                               HelperText="总铁含量目标值，通常在53-57%之间"
                                                               For="@(() => StateService.Target.TFeTarget)" />
                                            </MudItem>
                                            <MudItem xs="12">
                                                <MudNumericField T="decimal" @bind-Value="StateService.Target.RoTarget"
                                                               Label="碱度目标值"
                                                               Format="F2"
                                                               Min="1.0m" Max="3.0m"
                                                               Step="0.01m"
                                                               HelperText="CaO/SiO2比值，影响烧结矿强度"
                                                               For="@(() => StateService.Target.RoTarget)" />
                                            </MudItem>
                                        </MudGrid>
                                    </MudCardContent>
                                </MudCard>
                            </MudItem>

                            <MudItem xs="6">
                                <MudCard Class="mb-4">
                                    <MudCardHeader>
                                        <CardHeaderContent>
                                            <MudText Typo="Typo.h6">辅助成分目标</MudText>
                                        </CardHeaderContent>
                                    </MudCardHeader>
                                    <MudCardContent>
                                        <MudGrid>
                                            <MudItem xs="12">
                                                <MudNumericField T="decimal" @bind-Value="StateService.Target.MgOTarget"
                                                               Label="MgO目标值 (%)"
                                                               Format="F2"
                                                               Min="1.0m" Max="5.0m"
                                                               Step="0.01m"
                                                               HelperText="氧化镁含量，影响烧结矿还原性"
                                                               For="@(() => StateService.Target.MgOTarget)" />
                                            </MudItem>
                                            <MudItem xs="12">
                                                <MudNumericField T="decimal" @bind-Value="StateService.Target.Al2O3Target"
                                                               Label="Al2O3目标值 (%)"
                                                               Format="F2"
                                                               Min="1.0m" Max="4.0m"
                                                               Step="0.01m"
                                                               HelperText="氧化铝含量，影响烧结矿强度"
                                                               For="@(() => StateService.Target.Al2O3Target)" />
                                            </MudItem>
                                        </MudGrid>
                                    </MudCardContent>
                                </MudCard>
                            </MudItem>
                        </MudGrid>
                        
                        <!-- 目标参数预览 -->
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">目标参数预览</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudSimpleTable Dense="true" Hover="true">
                                    <thead>
                                        <tr>
                                            <th>成分</th>
                                            <th>目标值</th>
                                            <th>单位</th>
                                            <th>说明</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>TFe</strong></td>
                                            <td>@StateService.Target.TFeTarget.ToString("F2")</td>
                                            <td>%</td>
                                            <td>总铁含量，主要质量指标</td>
                                        </tr>
                                        <tr>
                                            <td><strong>碱度(R)</strong></td>
                                            <td>@StateService.Target.RoTarget.ToString("F2")</td>
                                            <td>-</td>
                                            <td>CaO/SiO2比值，影响强度</td>
                                        </tr>
                                        <tr>
                                            <td><strong>MgO</strong></td>
                                            <td>@StateService.Target.MgOTarget.ToString("F2")</td>
                                            <td>%</td>
                                            <td>氧化镁含量，影响还原性</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Al2O3</strong></td>
                                            <td>@StateService.Target.Al2O3Target.ToString("F2")</td>
                                            <td>%</td>
                                            <td>氧化铝含量，影响强度</td>
                                        </tr>
                                    </tbody>
                                </MudSimpleTable>
                            </MudCardContent>
                        </MudCard>
                        
                        <ValidationSummary />
                    </EditForm>
                </MudPaper>
            </ChildContent>
        </MudStep>

        <!-- 步骤3：约束条件设置 -->
        <MudStep Title="约束条件" Icon="Icons.Material.Filled.Rule">
            <ChildContent>
                <MudPaper Class="pa-6" Elevation="1">
                    <MudText Typo="Typo.h6" Class="mb-4">设置约束条件范围</MudText>
                    
                    <EditForm Model="StateService.Constraints" OnValidSubmit="SaveConstraints">
                        <DataAnnotationsValidator />
                        
                        <MudGrid>
                            <!-- 成分约束 -->
                            <MudItem xs="8">
                                <MudCard Class="mb-4">
                                    <MudCardHeader>
                                        <CardHeaderContent>
                                            <MudText Typo="Typo.h6">成分约束范围</MudText>
                                        </CardHeaderContent>
                                    </MudCardHeader>
                                    <MudCardContent>
                                        <MudGrid>
                                            <MudItem xs="6">
                                                <MudText Typo="Typo.subtitle1" Class="mb-2">TFe含量范围 (%)</MudText>
                                                <MudGrid>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.TFeMin"
                                                                       Label="最小值"
                                                                       Format="F1"
                                                                       Min="50m" Max="70m" />
                                                    </MudItem>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.TFeMax"
                                                                       Label="最大值"
                                                                       Format="F1"
                                                                       Min="50m" Max="70m" />
                                                    </MudItem>
                                                </MudGrid>
                                            </MudItem>

                                            <MudItem xs="6">
                                                <MudText Typo="Typo.subtitle1" Class="mb-2">碱度范围</MudText>
                                                <MudGrid>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.RoMin"
                                                                       Label="最小值"
                                                                       Format="F2"
                                                                       Min="1.0m" Max="3.0m" />
                                                    </MudItem>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.RoMax"
                                                                       Label="最大值"
                                                                       Format="F2"
                                                                       Min="1.0m" Max="3.0m" />
                                                    </MudItem>
                                                </MudGrid>
                                            </MudItem>

                                            <MudItem xs="6">
                                                <MudText Typo="Typo.subtitle1" Class="mb-2">MgO含量范围 (%)</MudText>
                                                <MudGrid>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.MgOMin"
                                                                       Label="最小值"
                                                                       Format="F1"
                                                                       Min="1.0m" Max="5.0m" />
                                                    </MudItem>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.MgOMax"
                                                                       Label="最大值"
                                                                       Format="F1"
                                                                       Min="1.0m" Max="5.0m" />
                                                    </MudItem>
                                                </MudGrid>
                                            </MudItem>

                                            <MudItem xs="6">
                                                <MudText Typo="Typo.subtitle1" Class="mb-2">Al2O3含量范围 (%)</MudText>
                                                <MudGrid>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.Al2O3Min"
                                                                       Label="最小值"
                                                                       Format="F1"
                                                                       Min="1.0m" Max="4.0m" />
                                                    </MudItem>
                                                    <MudItem xs="6">
                                                        <MudNumericField T="decimal" @bind-Value="StateService.Constraints.Al2O3Max"
                                                                       Label="最大值"
                                                                       Format="F1"
                                                                       Min="1.0m" Max="4.0m" />
                                                    </MudItem>
                                                </MudGrid>
                                            </MudItem>
                                        </MudGrid>
                                    </MudCardContent>
                                </MudCard>
                            </MudItem>
                            
                            <!-- 成本约束 -->
                            <MudItem xs="4">
                                <MudCard Class="mb-4">
                                    <MudCardHeader>
                                        <CardHeaderContent>
                                            <MudText Typo="Typo.h6">成本约束</MudText>
                                        </CardHeaderContent>
                                    </MudCardHeader>
                                    <MudCardContent>
                                        <MudText Typo="Typo.subtitle1" Class="mb-2">成本范围 (元/吨)</MudText>
                                        <MudGrid>
                                            <MudItem xs="12">
                                                <MudNumericField T="decimal" @bind-Value="StateService.Constraints.CostMin"
                                                               Label="最小成本"
                                                               Format="F0"
                                                               Min="0m" />
                                            </MudItem>
                                            <MudItem xs="12">
                                                <MudNumericField T="decimal" @bind-Value="StateService.Constraints.CostMax"
                                                               Label="最大成本"
                                                               Format="F0"
                                                               Min="0m" />
                                            </MudItem>
                                        </MudGrid>
                                        
                                        <MudDivider Class="my-3" />
                                        
                                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                                            成本约束将限制配料方案的总成本范围，确保经济性要求。
                                        </MudText>
                                    </MudCardContent>
                                </MudCard>
                            </MudItem>
                        </MudGrid>
                        
                        <!-- 约束验证状态 -->
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">约束验证状态</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                @if (StateService.ValidateConstraints())
                                {
                                    <MudAlert Severity="Severity.Success">
                                        <MudText><strong>约束条件验证通过</strong></MudText>
                                        <MudText>所有约束范围设置合理，可以进行优化计算。</MudText>
                                    </MudAlert>
                                }
                                else
                                {
                                    <MudAlert Severity="Severity.Error">
                                        <MudText><strong>约束条件验证失败</strong></MudText>
                                        <MudText>请检查约束范围设置，确保最小值小于最大值，且已选择原料。</MudText>
                                    </MudAlert>
                                }
                            </MudCardContent>
                        </MudCard>
                        
                        <ValidationSummary />
                    </EditForm>
                </MudPaper>
            </ChildContent>
        </MudStep>
    </MudStepper>

    <!-- 操作按钮 -->
    <MudPaper Class="pa-4 mt-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="6">
                <MudButton Variant="Variant.Outlined"
                         StartIcon="Icons.Material.Filled.ArrowBack"
                         OnClick="GoToPreviousStep"
                         Disabled="@(currentStep == 0)">
                    上一步
                </MudButton>
                <MudButton Variant="Variant.Filled"
                         Color="Color.Primary"
                         StartIcon="Icons.Material.Filled.ArrowForward"
                         OnClick="GoToNextStep"
                         Disabled="@(currentStep == 2)"
                         Class="ml-2">
                    下一步
                </MudButton>
            </MudItem>
            <MudItem xs="6" Class="text-right">
                <MudButton Variant="Variant.Filled" 
                         Color="Color.Success"
                         StartIcon="Icons.Material.Filled.Calculate"
                         OnClick="StartOptimization"
                         Disabled="@(!StateService.ValidateConstraints())">
                    开始优化计算
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    private MudStepper? stepper;
    private int currentStep = 0;

    protected override void OnInitialized()
    {
        StateService.OnStateChanged += StateHasChanged;
    }

    private string GetOptimizationCardClass(OptimizationType type)
    {
        return StateService.CurrentOptimizationType == type
            ? "mud-elevation-8 mud-theme-primary"
            : "mud-elevation-2";
    }

    private void SelectOptimizationType(OptimizationType type)
    {
        StateService.CurrentOptimizationType = type;
        StateService.NotifyStateChanged();
    }

    private void SaveTargetParameters()
    {
        StateService.NotifyStateChanged();
        NotificationService.ShowSuccess("目标参数已保存");
    }

    private void SaveConstraints()
    {
        StateService.NotifyStateChanged();
        NotificationService.ShowSuccess("约束条件已保存");
    }

    private void GoToPreviousStep()
    {
        if (currentStep > 0)
        {
            currentStep--;
        }
    }

    private void GoToNextStep()
    {
        if (currentStep == 0 && StateService.CurrentOptimizationType != OptimizationType.CostOptimal && StateService.CurrentOptimizationType != OptimizationType.QualityOptimal)
        {
            NotificationService.ShowWarning("请先选择优化目标类型");
            return;
        }

        if (currentStep < 2)
        {
            currentStep++;
        }
    }

    private void StartOptimization()
    {
        if (StateService.ValidateConstraints())
        {
            // 根据优化类型导航到相应页面
            var targetPage = StateService.CurrentOptimizationType == OptimizationType.CostOptimal
                ? "/optimization/cost"
                : "/optimization/quality";

            Navigation.NavigateTo(targetPage);
        }
        else
        {
            NotificationService.ShowError("请完善配置参数后再开始优化");
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
