{"GlobalPropertiesHash": "zB8K0Eu61Dq0QhXCvrpgZzzp0FHyVQQnZVK9WKRVh44=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["HvOzT8oTDdan+rWWvuNtu9aitPSiFtcRqVaigPFNlrc=", "d7FtFL7GX9rzEVrGF9EtDrhLibCMwg7YV/0g5J7+2b4=", "z54SHrFfTr03q+UnFzB9Prpo3udN8QeHBsZLuMWKDx8=", "M0WXGESwefImKsR3c+WlWCTZjgLcgVhNaSpZWpCs4Wk=", "IxHSUQzBkwYBv8GCU5bpjQSIfLOKdwuejG5oZiNO7P0=", "5eaNcPDr4YvCAcT4DgyJC6GhAhrNStr3N935bspmzkE=", "KJtPIPbnS5s50qoTk07DmuRbyiKbkRXrO7uaMmlz5Oc=", "IK8vGpnbxWU0226bskaH6AM8SQaXfRZKnt30pGV9dxc=", "McnJfQiNPb4Lme2J4OxHTgT7r7EUlCXQ5z2PUiQhzLk=", "+/9973lKxBjPakWrTr6NF6MiPfgUNpqzpY5IlFfJCBo=", "ojmSalpQB68PthUX1PORdAucfTud/5ttCPWexfs6uh8=", "ad+RP7hfEekaY6T5R4h7Q+AtFsaJSFknFd/GtB2//h0=", "i6SpPM1qQ9MHwS6ihBysgcFHniKVTE+U4RfocD6WPYE=", "uR1jeY8GiYQcPLdrlCe0REBK243vhFYr6MCsZ1FEOzY=", "IWkc58GXBc+RhGj2QfDs3gDsdvr/KyXf5A446VeWgLM=", "K77vvHdcqx5RTo+J0Wz8ItRtHLJxGHlme9Y6Jwv6vU4=", "i88cqCkjl3/HRn9dYMJk9VMtUI8t+sut5pinhV4NGHM=", "BIoqEqO7jL/rC+OtT0a0rcCLCm8WVqzLJ1Jpg818yTQ=", "lBtXwPMQ1NT9uPUvLD49pttb0rSrLq4b8QspDZUbEvc=", "Y5n82vtJ7L9mmElCcdHvLC5SJ4lWatpVJ5S0zjNWYcM=", "11Pc/NilOgQexAHXcYcA+X+1em7uEQOEEqu1B8c3zZY=", "s7TkAhjnjGckX1i7ljZB738pxbQi+af1k7yI1VqVAME=", "kKcDR/Dgxz7NVpeyWiTR+8EHDZU0wZSPWPjgSzVWkjk=", "ekMb0n2n2iF8MhEZmb7hjgvYHYRo4jUWZOBaqHItMWA=", "P0f4lTsbp62Ju6ztKsY2ceYiIU4K4tk+sw6EhlxlERM=", "FkCJEnjCOgJrac3rmd8KKdew2Ckqscz9uKdSpGiSkHA=", "bOHke6GXGVAWsNVfNXWz8CRMUA2UtBqLS/7DWytrMJY=", "8cxG6DHROr0i90BEW2iOz3u7Z3MIvEBj47N6L49hyBU=", "0bIeNGujLo+NKFK/lU6+G8e++V0zpoorBPGD72euCjw=", "G+FZlNYpvEW6G0+nVl3+THym5ZXWBrhOU/eQGJ2hc5c=", "DCYFAy7krDCsDkYHQD4ED7asYhR7+chqRVN61FWsfR0=", "cyawYhpbuUQ5ZM1spegNWCoDAW+tuBB9CyXNJTAL5qg=", "k9IlTTEyCdFPJt0AzPv4o54bJOBdWPMALnfhE7xavfs=", "akYAsUpRla3/SiR7v0iTT7x6F8ONe0TeCVGCHgkkRN0=", "dBCpS61GqA4efeUnpbShRBv/s+ZcT7A+zSWQdngn+EY=", "X3dw9CqNjPPhCN+hsPaRo7tQI2T/PB9KShwMW7sQXN0=", "jdjyyEXnZWU+REFBDq0s1DZhmLbf8IUnLTxAAfbQyj0=", "sOGbeK5KjzWtdD0ImRsGhf739Z66c7sqCsyJrVWOULk=", "Wi5ZCjXiabGa23GHz7dl+8zZYWhzLHIuwBYucesvU8A=", "0J0qKlivf4ce5d2+YfSIs/UtCJBET243M3+oQo0ZYkc=", "Ah7Xjb38MLp2qDFsfS0Q18yBmZuRfGfSlExEZSVhxvg=", "2sdS60Gea8fJiW60nhu4gyCOREDbuOHlLNlnjrH/Wjg=", "jWvg54H0+Hupu7XBqzu2b+mRTKHOylbtobUTvRq/ZW0=", "lmA9XKNRBxAseRMAXooK74w+/+hBlgltM9kt7y01jjI=", "eCaOpDtMoh06Bi4JmpZrC2bh4upCw0i0Z18lREnYXtU=", "rIPeLr1/F1NhNmLLmzq7z2ux6Q35XKm7paW9uIYSxyk=", "WUEj92Uy346ZpZ7ii27tOr7+kRyp2WBHX7IPNEwLnDg=", "+tY+N7W5GLf16evPLkdhDkW6GIl9rjjgAZvXfxKmxS4=", "uuQ9zxqnvnq8qHj2L9GWJe9jNHO1CMDeRi5XNv8RrCQ=", "thidEUOWwzUNIGUhMImGf4ArrhcOp4rvlRekyZ54DpQ=", "6xcPEdKXQXQnGd/+fVGNyFAWXzPMzOJcDGMQiJ4hgMU=", "6TklMdEoJ17gIMVAE+nSeaGRiA6awIhmzxVnRe0H3Mw=", "rdqbWsKmILEyE4hdEawrqdTmjiJ3rjJmIzaumbqXdAI=", "eroxv5uedIZr/s4UWnjjNrtfZNHJE2iJAmWZ6FIT3vc=", "p9A5zNaqTMbs3tYXP7Gz9QJGuNY8fQhAy5n+aTsWbcA=", "pRciNirsQWdi+MGg/kH7jPLiWh40KUmVxA1VK5YjJIU=", "AU5yGxY3NCyJV0rn7XOObfjzBT5HktXn0rsDVo50nlE=", "ejnuGCdz9GDCPJjkhMBS+cYEQZGAf2LnTD9iEHPQgt4="], "CachedAssets": {"HvOzT8oTDdan+rWWvuNtu9aitPSiFtcRqVaigPFNlrc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\css\\app.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "css/app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8rbvw3on5j", "Integrity": "vGbjgqEqv4y3q5OB8W2R9LthkuF8mQfHFeNdKSReSmU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\app.css", "FileLength": 4151, "LastWriteTime": "2025-07-30T00:29:57.9359116+00:00"}, "d7FtFL7GX9rzEVrGF9EtDrhLibCMwg7YV/0g5J7+2b4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\favicon.png", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-07-30T00:29:58.1076982+00:00"}, "z54SHrFfTr03q+UnFzB9Prpo3udN8QeHBsZLuMWKDx8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\icon-192.png", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "icon-192#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f9uvjujlxy", "Integrity": "DbpQaq68ZSb5IoPosBErM1QWBfsbTxpJqhU0REi6wP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\icon-192.png", "FileLength": 2626, "LastWriteTime": "2025-07-30T00:29:58.1097925+00:00"}, "M0WXGESwefImKsR3c+WlWCTZjgLcgVhNaSpZWpCs4Wk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\index.html", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f14of2wzb9", "Integrity": "jV4FSyhmgxWRtwY53ngLMAG1rZbk5/b/Mt2N8xla/Oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 995, "LastWriteTime": "2025-07-30T00:29:58.120683+00:00"}, "IxHSUQzBkwYBv8GCU5bpjQSIfLOKdwuejG5oZiNO7P0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-30T00:29:58.1231075+00:00"}, "5eaNcPDr4YvCAcT4DgyJC6GhAhrNStr3N935bspmzkE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-30T00:29:58.1284308+00:00"}, "KJtPIPbnS5s50qoTk07DmuRbyiKbkRXrO7uaMmlz5Oc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-30T00:29:58.1304253+00:00"}, "IK8vGpnbxWU0226bskaH6AM8SQaXfRZKnt30pGV9dxc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-30T00:29:58.1529108+00:00"}, "McnJfQiNPb4Lme2J4OxHTgT7r7EUlCXQ5z2PUiQhzLk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-30T00:29:58.1576064+00:00"}, "+/9973lKxBjPakWrTr6NF6MiPfgUNpqzpY5IlFfJCBo=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-30T00:29:58.1663998+00:00"}, "ojmSalpQB68PthUX1PORdAucfTud/5ttCPWexfs6uh8=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-30T00:29:58.1683952+00:00"}, "ad+RP7hfEekaY6T5R4h7Q+AtFsaJSFknFd/GtB2//h0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-30T00:29:58.1824307+00:00"}, "i6SpPM1qQ9MHwS6ihBysgcFHniKVTE+U4RfocD6WPYE=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-30T00:29:58.1859813+00:00"}, "uR1jeY8GiYQcPLdrlCe0REBK243vhFYr6MCsZ1FEOzY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-30T00:29:58.2088117+00:00"}, "IWkc58GXBc+RhGj2QfDs3gDsdvr/KyXf5A446VeWgLM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-30T00:29:58.2108051+00:00"}, "K77vvHdcqx5RTo+J0Wz8ItRtHLJxGHlme9Y6Jwv6vU4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-30T00:29:58.2143084+00:00"}, "i88cqCkjl3/HRn9dYMJk9VMtUI8t+sut5pinhV4NGHM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-30T00:29:58.2312813+00:00"}, "BIoqEqO7jL/rC+OtT0a0rcCLCm8WVqzLJ1Jpg818yTQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-30T00:29:58.2363622+00:00"}, "lBtXwPMQ1NT9uPUvLD49pttb0rSrLq4b8QspDZUbEvc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-30T00:29:58.2383837+00:00"}, "Y5n82vtJ7L9mmElCcdHvLC5SJ4lWatpVJ5S0zjNWYcM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-30T00:29:58.2414089+00:00"}, "11Pc/NilOgQexAHXcYcA+X+1em7uEQOEEqu1B8c3zZY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-30T00:29:58.2484871+00:00"}, "s7TkAhjnjGckX1i7ljZB738pxbQi+af1k7yI1VqVAME=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-30T00:29:58.2564339+00:00"}, "kKcDR/Dgxz7NVpeyWiTR+8EHDZU0wZSPWPjgSzVWkjk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-30T00:29:58.2584244+00:00"}, "ekMb0n2n2iF8MhEZmb7hjgvYHYRo4jUWZOBaqHItMWA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-30T00:29:57.6701892+00:00"}, "P0f4lTsbp62Ju6ztKsY2ceYiIU4K4tk+sw6EhlxlERM=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-30T00:29:57.6752328+00:00"}, "FkCJEnjCOgJrac3rmd8KKdew2Ckqscz9uKdSpGiSkHA=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-30T00:29:57.6908895+00:00"}, "bOHke6GXGVAWsNVfNXWz8CRMUA2UtBqLS/7DWytrMJY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-30T00:29:57.6949234+00:00"}, "8cxG6DHROr0i90BEW2iOz3u7Z3MIvEBj47N6L49hyBU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-30T00:29:57.7175242+00:00"}, "0bIeNGujLo+NKFK/lU6+G8e++V0zpoorBPGD72euCjw=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-30T00:29:57.7279857+00:00"}, "G+FZlNYpvEW6G0+nVl3+THym5ZXWBrhOU/eQGJ2hc5c=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-30T00:29:57.7675279+00:00"}, "DCYFAy7krDCsDkYHQD4ED7asYhR7+chqRVN61FWsfR0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-30T00:29:57.7766009+00:00"}, "cyawYhpbuUQ5ZM1spegNWCoDAW+tuBB9CyXNJTAL5qg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-30T00:29:57.814094+00:00"}, "k9IlTTEyCdFPJt0AzPv4o54bJOBdWPMALnfhE7xavfs=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-30T00:29:57.8235312+00:00"}, "akYAsUpRla3/SiR7v0iTT7x6F8ONe0TeCVGCHgkkRN0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-30T00:29:57.85238+00:00"}, "dBCpS61GqA4efeUnpbShRBv/s+ZcT7A+zSWQdngn+EY=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-30T00:29:57.8611896+00:00"}, "X3dw9CqNjPPhCN+hsPaRo7tQI2T/PB9KShwMW7sQXN0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-30T00:29:57.9038408+00:00"}, "jdjyyEXnZWU+REFBDq0s1DZhmLbf8IUnLTxAAfbQyj0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vpl3cor3y8", "Integrity": "TRN3HEAp4oSe08eF40BHL2rF7U6i6jwDWKFnjdV5i8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-30T00:29:57.9108346+00:00"}, "sOGbeK5KjzWtdD0ImRsGhf739Z66c7sqCsyJrVWOULk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m8m04ow07k", "Integrity": "llRPAjsTCQdX9I6jAgtmhr1rUT9tvAVdaPQrYD38Td4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-30T00:29:57.929568+00:00"}, "Wi5ZCjXiabGa23GHz7dl+8zZYWhzLHIuwBYucesvU8A=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-30T00:29:57.9339168+00:00"}, "0J0qKlivf4ce5d2+YfSIs/UtCJBET243M3+oQo0ZYkc=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "07r8e49zdp", "Integrity": "rOniCgnoNJIVZp7PCJnzSGta8mgTGweOpxp/z4+Oc+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-30T00:29:57.9600063+00:00"}, "Ah7Xjb38MLp2qDFsfS0Q18yBmZuRfGfSlExEZSVhxvg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tvzo76q7ay", "Integrity": "V5Xdos7LtFT/h3fFsCcdiNL4c4uaPm3I9zR8R6dGsO4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-30T00:29:57.9664993+00:00"}, "2sdS60Gea8fJiW60nhu4gyCOREDbuOHlLNlnjrH/Wjg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sybl3t77fa", "Integrity": "1NZ0e0qqYzJigO5XFb9Y2bgUwRxi1f/jpwe4eU6A5pA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-30T00:29:57.9893993+00:00"}, "jWvg54H0+Hupu7XBqzu2b+mRTKHOylbtobUTvRq/ZW0=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-30T00:29:57.9945359+00:00"}, "lmA9XKNRBxAseRMAXooK74w+/+hBlgltM9kt7y01jjI=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3orf5n9fb", "Integrity": "ygX//9b5IsEzbh1wXO6jfbN5R0YVHtui5Zm2Pxzfows=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-30T00:29:58.0192003+00:00"}, "eCaOpDtMoh06Bi4JmpZrC2bh4upCw0i0Z18lREnYXtU=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m3in5dwigm", "Integrity": "YrT1R620xatdJbaXLTTytJFw9ifPcUfLyXugHtFHdvQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-30T00:29:58.0241846+00:00"}, "rIPeLr1/F1NhNmLLmzq7z2ux6Q35XKm7paW9uIYSxyk=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xxsbxg4q4i", "Integrity": "u9e0mnJh0mj/d32Mx6N9Ibj/0+LtjionqG5TWUyotPk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-30T00:29:58.0459245+00:00"}, "WUEj92Uy346ZpZ7ii27tOr7+kRyp2WBHX7IPNEwLnDg=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-30T00:29:58.0515843+00:00"}, "+tY+N7W5GLf16evPLkdhDkW6GIl9rjjgAZvXfxKmxS4=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "knl5pustf3", "Integrity": "IBIemhxt8rrB7DVR6DoN0KwZECFkw69mvMSn40GJXsc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-30T00:29:58.0620826+00:00"}, "uuQ9zxqnvnq8qHj2L9GWJe9jNHO1CMDeRi5XNv8RrCQ=": {"Identity": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\sample-data\\weather.json", "SourceId": "SinterBlendingSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\wwwroot\\", "BasePath": "/", "RelativePath": "sample-data/weather#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iag0ou56lh", "Integrity": "enKgCMkYmCpfEcmg6Annbmc40VZ/A6aYYSQjZfVn2cU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\sample-data\\weather.json", "FileLength": 453, "LastWriteTime": "2025-07-30T00:29:57.9620014+00:00"}}, "CachedCopyCandidates": {}}