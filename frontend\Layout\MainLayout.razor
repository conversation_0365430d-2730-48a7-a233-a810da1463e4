﻿@inherits LayoutComponentBase
@inject IJSRuntime JSRuntime

<MudThemeProvider Theme="@_theme" />
<MudDialogProvider />
<MudSnackbarProvider />

<MudLayout>
    <MudAppBar Elevation="1" Color="Color.Primary" Fixed="true" Dense="true">
        <MudIconButton Icon="Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />
        <MudText Typo="Typo.h6" Class="ml-3">烧结配料计算模型系统</MudText>
        <MudSpacer />
        <MudIconButton Icon="Icons.Material.Filled.Notifications" Color="Color.Inherit" />
        <MudIconButton Icon="Icons.Material.Filled.Settings" Color="Color.Inherit" />
        <MudText Typo="Typo.body2" Class="mr-4">@DateTime.Now.ToString("yyyy-MM-dd HH:mm")</MudText>
    </MudAppBar>

    <MudDrawer @bind-Open="_drawerOpen" Elevation="2" Variant="@DrawerVariant.Mini" OpenMiniOnHover="true">
        <MudDrawerHeader Class="pa-6">
            <MudText Typo="Typo.h6" Color="Color.Primary">SBS</MudText>
        </MudDrawerHeader>
        <NavMenu />
    </MudDrawer>

    <MudMainContent Class="pt-16 px-4">
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-0">
            @Body
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    private bool _drawerOpen = true;

    // 工业风格主题配置
    private readonly MudTheme _theme = new()
    {
        PaletteLight = new PaletteLight()
        {
            Primary = "#165DFF",
            Secondary = "#0FC6C2",
            Success = "#00B42A",
            Warning = "#FF7D00",
            Error = "#F53F3F",
            Background = "#F5F7FA",
            Surface = "#FFFFFF",
            AppbarBackground = "#165DFF",
            DrawerBackground = "#FFFFFF",
            TextPrimary = "#1D2129",
            TextSecondary = "#4E5969",
            ActionDefault = "#86909C",
            ActionDisabled = "#C9CDD4",
            Divider = "#E5E6EB"
        }
    };

    private void ToggleDrawer()
    {
        _drawerOpen = !_drawerOpen;
    }
}
