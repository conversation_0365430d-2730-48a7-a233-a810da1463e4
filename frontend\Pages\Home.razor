﻿@page "/"
@inject IStateService StateService
@inject IOptimizationService OptimizationService

<PageTitle>系统概览</PageTitle>

<MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
    <!-- 系统标题 -->
    <MudPaper Class="pa-6 mb-4" Elevation="2">
        <MudGrid AlignItems="Center">
            <MudItem xs="8">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="Icons.Material.Filled.Dashboard" Class="mr-3" Size="Size.Large" />
                    烧结配料计算模型系统
                </MudText>
                <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">
                    基于SQP二次序列规划算法的智能配料优化平台
                </MudText>
            </MudItem>
            <MudItem xs="4" Class="text-right">
                <MudChip T="string" Color="Color.Success" Size="Size.Large">
                    <MudIcon Icon="Icons.Material.Filled.CheckCircle" Class="mr-1" />
                    系统运行正常
                </MudChip>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <!-- 功能卡片 -->
    <MudGrid>
        <!-- 原料管理 -->
        <MudItem xs="4">
            <MudCard Class="pa-4" Style="height: 200px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Inventory"
                           Size="Size.Large"
                           Color="Color.Secondary"
                           Class="mb-3" />
                    <MudText Typo="Typo.h6" Class="mb-2">原料管理</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        管理 @StateService.Materials.Count 种原料的化学成分和价格参数
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Secondary"
                             Href="/materials"
                             StartIcon="Icons.Material.Filled.Edit">
                        编辑原料
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- 优化配置 -->
        <MudItem xs="4">
            <MudCard Class="pa-4" Style="height: 200px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Settings"
                           Size="Size.Large"
                           Color="Color.Warning"
                           Class="mb-3" />
                    <MudText Typo="Typo.h6" Class="mb-2">优化配置</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        设置目标成分和约束条件，选择优化策略
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Warning"
                             Href="/optimization/target"
                             StartIcon="Icons.Material.Filled.Target">
                        配置参数
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>

        <!-- 方案计算 -->
        <MudItem xs="4">
            <MudCard Class="pa-4" Style="height: 200px;">
                <MudCardContent Class="text-center">
                    <MudIcon Icon="Icons.Material.Filled.Calculate"
                           Size="Size.Large"
                           Color="Color.Success"
                           Class="mb-3" />
                    <MudText Typo="Typo.h6" Class="mb-2">方案计算</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        执行SQP算法，获得成本最优和质量最优方案
                    </MudText>
                </MudCardContent>
                <MudCardActions Class="justify-center">
                    <MudButton Variant="Variant.Filled"
                             Color="Color.Success"
                             Href="/optimization/cost"
                             StartIcon="Icons.Material.Filled.PlayArrow">
                        开始计算
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 系统状态 -->
    <MudPaper Class="pa-4 mt-4" Elevation="2">
        <MudText Typo="Typo.h6" Class="mb-3">
            <MudIcon Icon="Icons.Material.Filled.Info" Class="mr-2" />
            系统状态
        </MudText>
        <MudGrid>
            <MudItem xs="3">
                <MudCard Class="pa-3">
                    <MudCardContent Class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Primary">@StateService.Materials.Count</MudText>
                        <MudText Typo="Typo.body2">原料总数</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="3">
                <MudCard Class="pa-3">
                    <MudCardContent Class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Secondary">@StateService.SelectedMaterials.Count</MudText>
                        <MudText Typo="Typo.body2">已选原料</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="3">
                <MudCard Class="pa-3">
                    <MudCardContent Class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Success">@StateService.Target.TFeTarget.ToString("F1")%</MudText>
                        <MudText Typo="Typo.body2">TFe目标</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="3">
                <MudCard Class="pa-3">
                    <MudCardContent Class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Warning">@StateService.Target.RoTarget.ToString("F2")</MudText>
                        <MudText Typo="Typo.body2">碱度目标</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudContainer>

@code {
    protected override void OnInitialized()
    {
        StateService.OnStateChanged += StateHasChanged;

        // 初始化状态
        if (!StateService.Materials.Any())
        {
            // 这里可以加载默认数据
        }
    }

    public void Dispose()
    {
        StateService.OnStateChanged -= StateHasChanged;
    }
}
