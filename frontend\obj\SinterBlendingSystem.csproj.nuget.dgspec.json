{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\SinterBlendingSystem.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\SinterBlendingSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\SinterBlendingSystem.csproj", "projectName": "SinterBlendingSystem", "projectPath": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\SinterBlendingSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\7.30\\frontend\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Visual Studio\\shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Components.WebAssembly": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.Components.WebAssembly.DevServer": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}, "Microsoft.NET.Sdk.WebAssembly.Pack": {"suppressParent": "All", "target": "Package", "version": "[9.0.7, )", "autoReferenced": true}, "MudBlazor": {"target": "Package", "version": "[8.10.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Runtime.Mono.browser-wasm", "version": "[9.0.7, 9.0.7]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"browser-wasm": {"#import": []}}}}}