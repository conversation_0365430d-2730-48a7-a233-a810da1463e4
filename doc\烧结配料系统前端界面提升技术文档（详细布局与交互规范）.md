# 烧结配料系统前端界面提升技术文档（详细布局与交互规范）

## 一、设计原则与技术栈

### 1.1 核心设计原则



*   **工业级交互标准**：操作路径最短化（核心功能 3 步内可达）、数据可视化优先（关键指标图表化）、容错性设计（操作可逆 + 二次确认）

*   **响应式适配**：支持 1920×1080（主场景）、1366×768（操作站）、平板横屏（移动巡检）三级分辨率

*   **视觉一致性**：采用蓝灰工业色系（主色 #165DFF，辅助色 #0FC6C2，警告色 #FF7D00，错误色 #F53F3F）

### 1.2 技术栈细节



*   **框架**：Blazor WebAssembly 6.0（组件化复用率≥80%）

*   **UI 组件库**：基于 MudBlazor 二次封装（定制工业风格主题）

*   **图表引擎**：ECharts 5.4.3（烧结曲线用折线图，配比对比用环形图，成分偏差用雷达图）

*   **状态管理**：Blazor State（全局状态：当前原料集 / 目标参数；局部状态：表单输入 / 表格选中项）

## 二、核心页面详细布局与组件规范

### 2.1 原料参数编辑页（核心交互区）

#### 2.1.1 整体布局（12 列栅格系统）



| 区域      | 栅格列占比 | 功能定位          | 高度规范                |
| ------- | ----- | ------------- | ------------------- |
| 顶部操作栏   | 12    | 页面标题 + 批量操作按钮 | 60px                |
| 左侧原料表格区 | 7     | 展示 / 编辑原料列表   | calc(100vh - 180px) |
| 右侧参数编辑区 | 5     | 详情表单 + 校验提示   | 同左侧表格区              |
| 底部状态栏   | 12    | 数据统计 + 状态提示   | 30px                |

#### 2.1.2 组件细节规范



1.  **顶部操作栏**

*   标题：`原料参数管理`（20px 粗体，左对齐，距左 20px）

*   按钮组（右对齐，间距 10px）：


    *   `新增原料`（主色按钮，图标 + 文字： 新增）

    *   `批量导入`（次要按钮，含下拉菜单：Excel 导入 / 模板下载）

    *   `保存修改`（成功状态按钮，仅在有修改时启用）

    *   `刷新数据`（图标按钮：）

1.  **左侧原料表格（MudDataGrid 组件）**

*   列配置（固定列宽 + 自适应）：



| 列名         | 宽度    | 数据类型 | 交互特性                              |
| ---------- | ----- | ---- | --------------------------------- |
| 选择         | 50px  | 复选框  | 支持全选 / 反选，选中行高亮（背景色 #E8F4FD）      |
| 原料编号       | 80px  | 文本   | 左对齐，不可编辑                          |
| 原料名称       | 120px | 文本   | 行内编辑（双击激活输入框）                     |
| 湿基 TFe (%) | 100px | 数字   | 行内编辑（限制 2 位小数，范围 0-100）           |
| 水分 (%)     | 80px  | 数字   | 行内编辑（限制 1 位小数，范围 0-20，超限时输入框边框变红） |
| 烧损率 (%)    | 100px | 数字   | 行内编辑（限制 1 位小数）                    |
| 操作         | 100px | 按钮组  | 编辑（）/ 删除（）                        |



*   分页控件：底部居中，每页 20 行，显示总条数（`共32条，当前1-20条`）

*   排序：点击列头排序（默认按原料编号升序）

1.  **右侧参数编辑区（卡片式布局）**

*   卡片标题：`原料详情`（16px 粗体，padding 15px）

*   表单布局（两列栅格，行间距 12px）：


    *   第一列（左）：标签（右对齐，宽度 80px）+ 输入框（占剩余宽度）

    *   第二列（右）：同左，与第一列间距 20px

*   关键字段验证规则：


    *   水分：`>20%` 时下方显示警告提示（ 水分过高可能影响配比精度）

    *   配比上限：必须 `>配比下限`，否则显示错误提示（红色文字：`上限必须大于下限`）

*   底部按钮：`应用到选中行`（当修改非当前行数据时显示，点击将当前表单值批量更新到选中行）

1.  **底部状态栏**

*   左侧：`已加载原料：32种 | 待保存修改：5项`（灰色文字）

*   右侧：`最后更新：2025-07-30 14:23:15`（浅色文字）

### 2.2 目标与约束配置页

#### 2.2.1 整体布局



*   采用分步表单模式（3 个步骤面板，左侧步骤导航）

*   总高度：`calc(100vh - 100px)`，宽度 100%，内边距 20px

#### 2.2.2 步骤 1：优化目标选择（步骤面板 1）



*   标题：`选择优化目标`（18px 粗体，margin-bottom 20px）

*   选项卡布局（MudTabs）：


    *   选项卡 1：`成本最优`（含图标）


        *   内容区：成本权重滑块（0-100%）+ 说明文本（`优先降低原料采购成本，允许成分在目标范围内波动`）

    *   选项卡 2：`质量最优`（含图标）


        *   内容区：质量权重滑块 + 说明文本（`优先保证成分达标，成本控制在预算内`）

*   底部按钮：`下一步`（居右，点击切换到步骤 2）

#### 2.2.3 步骤 2：成分约束配置（步骤面板 2）



*   采用分组卡片布局（4 个卡片，2×2 排列）：


    *   卡片 1：`TFe约束`


        *   内容：双滑块组件（范围 58-62%）+ 输入框组（最小值：\[] % 最大值：\[] %），两者双向绑定

    *   卡片 2：`碱度(Ro)约束`


        *   内容：双滑块（1.8-2.2）+ 输入框组，附加说明文字（`Ro=CaO/SiO₂`）

    *   卡片 3：`MgO/Al₂O₃约束`


        *   内容：MgO（1.5-3.0%）输入框 + Al₂O₃（<1.8%）输入框

    *   卡片 4：`有害元素约束`


        *   内容：P（<0.15%）+ S（<0.08%）输入框，超限时显示红色警告图标

*   底部按钮：`上一步` | `下一步`

#### 2.2.4 步骤 3：配比范围约束（步骤面板 3）



*   表格布局（同原料表格，但仅显示配比上下限列）：


    *   列：原料名称 | 配比下限 (%) | 配比上限 (%) | 约束类型（下拉框：严格 / 弹性）

*   交互：支持批量设置（选中多行后点击`统一设置下限`按钮）

*   底部按钮：`上一步` | `完成配置`（点击后触发参数校验，通过则进入结果页）

### 2.3 结果展示页（多方案对比）

#### 2.3.1 顶部布局



*   筛选栏：`计算日期`（日期选择器）+ `方案状态`（下拉框：全部 / 已采用 / 已废弃）

*   操作按钮：`导出Excel` + `打印方案` + `对比分析`（弹出对比窗口）

#### 2.3.2 方案切换区（MudTabs）



*   选项卡 1：`成本最优方案`（显示 3 个方案，按成本升序排列）


    *   表格列：方案 ID | 湿配比详情 | 单位成本 (元 / 吨) | TFe (%) | Ro | 操作（采用 / 查看详情）

    *   成本最低方案行高亮（背景色 #E6F7ED）

*   选项卡 2：`质量最优方案`


    *   表格列：方案 ID | 湿配比详情 | TFe 偏差 (%) | Ro 偏差 (%) | MgO (%) | 操作

    *   偏差最小方案行高亮

#### 2.3.3 详情弹窗（点击 "查看详情" 打开）



*   布局：左侧参数表 + 右侧图表区


    *   参数表：分 3 组（原料配比 / 烧结矿成分 / 成本指标）

    *   图表区：


        *   饼图：原料配比占比（）

        *   雷达图：成分偏差对比（目标值 vs 计算值）

        *   趋势图：成本 - 质量平衡曲线

## 三、交互增强细节规范

### 3.1 实时校验与反馈



*   **输入实时校验**：


    *   数字输入框：失焦时校验范围，超范围立即显示错误提示（红色文字 + 图标）

    *   配比总和：实时计算所有原料湿配比总和，偏离 100% 时在底部状态栏提示（`总和：102% <i class="fas fa-exclamation-circle"></i> 请调整`）

*   **操作反馈**：


    *   保存成功：右上角弹出成功通知（ 已保存 3 条修改），持续 3 秒

    *   计算中：显示进度条（线性进度，文案：`正在计算最优方案...(35%)`）

    *   无可行解：全屏半透明遮罩 + 警告弹窗（含详细原因：`低TFe原料占比过高，无法满足目标TFe≥58%`）

### 3.2 响应式适配规则



*   **桌面端（≥1200px）**：三页面独立展示，支持同时打开多个标签页

*   **平板端（768px-1199px）**：


    *   原料编辑页：右侧表单折叠为抽屉式（点击编辑按钮弹出）

    *   结果页：选项卡改为下拉选择器

*   **移动端（<768px）**：


    *   所有表格启用横向滚动

    *   分步表单改为全屏占比，步骤导航改为顶部进度条

### 3.3 无障碍设计细节



*   **键盘导航**：


    *   Tab 键顺序：按操作流程（表格→表单→按钮）

    *   表格行选中：支持↑↓箭头键切换

*   **屏幕阅读器支持**：


    *   所有按钮 / 输入框添加 aria-label（例：`<button aria-label="保存当前修改的原料参数">保存</button>`）

    *   错误提示添加 aria-live="polite"（实时朗读）

## 四、前端性能优化策略



1.  **数据加载优化**：

*   原料数据采用分页加载（默认 20 条 / 页），滚动到底部自动加载下一页

*   图表数据按需渲染（切换选项卡时才加载对应图表数据）

1.  **组件渲染优化**：

*   表格行使用虚拟滚动（MudVirtualize），超过 50 行时启用

*   表单输入防抖（300ms 延迟），避免频繁触发校验

1.  **缓存策略**：

*   原料基础数据本地缓存（localStorage，有效期 1 小时）

*   计算结果会话缓存（sessionStorage，页面关闭后清除）

## 五、UI 组件库定制规范（基于 MudBlazor）



| 组件类型 | 定制项  | 规范值                                                     |
| ---- | ---- | ------------------------------------------------------- |
| 按钮   | 主色按钮 | 背景 #165DFF，hover#0E42D2，圆角 4px                          |
| 输入框  | 错误状态 | 边框 #F53F3F，背景 #FFF2F3，右侧显示                              |
| 表格   | 表头   | 背景 #F5F7FA，文字 #1D2129，高度 48px                           |
| 卡片   | 边框   | 1px solid #E5E6EB，圆角 8px，阴影 0 2px 8px rgba (0,0,0,0.08) |

（附：完整主题配置代码见附录 A）

## 附录 A：主题定制代码示例



```
// Program.cs中配置MudBlazor主题

builder.Services.AddMudServices(config =>

{

&#x20;   config.Theme = new Theme()

&#x20;   {

&#x20;       Palette = new Palette()

&#x20;       {

&#x20;           Primary = "#165DFF",

&#x20;           Success = "#00B42A",

&#x20;           Error = "#F53F3F",

&#x20;           Warning = "#FF7D00"

&#x20;       },

&#x20;       Typography = new Typography()

&#x20;       {

&#x20;           H3 = new TypographyVariant() { FontSize = "1.5rem", FontWeight = 600 },

&#x20;           Body1 = new TypographyVariant() { FontSize = "0.875rem", LineHeight = 1.5 }

&#x20;       }

&#x20;   };

});
```

> （注：文档部分内容可能由 AI 生成）