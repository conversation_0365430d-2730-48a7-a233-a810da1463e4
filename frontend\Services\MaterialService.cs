using System.Net.Http.Json;
using SinterBlendingSystem.Models;

namespace SinterBlendingSystem.Services;

public class MaterialService : IMaterialService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly INotificationService _notification;
    private List<MaterialModel> _materials = new();

    public MaterialService(HttpClient httpClient, ILocalStorageService localStorage, INotificationService notification)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _notification = notification;
        InitializeDefaultMaterials();
    }

    public async Task<List<MaterialModel>> GetMaterialsAsync()
    {
        try
        {
            // 首先尝试从本地存储加载
            var cached = await _localStorage.GetItemAsync<List<MaterialModel>>("materials");
            if (cached != null && cached.Any())
            {
                _materials = cached;
                return _materials;
            }

            // 如果本地没有数据，返回默认数据
            return _materials;
        }
        catch (Exception ex)
        {
            _notification.ShowError($"加载原料数据失败: {ex.Message}");
            return _materials;
        }
    }

    public async Task<MaterialModel?> GetMaterialAsync(int id)
    {
        var materials = await GetMaterialsAsync();
        return materials.FirstOrDefault(m => m.Id == id);
    }

    public async Task<bool> SaveMaterialAsync(MaterialModel material)
    {
        try
        {
            var materials = await GetMaterialsAsync();
            var existing = materials.FirstOrDefault(m => m.Id == material.Id);
            
            if (existing != null)
            {
                var index = materials.IndexOf(existing);
                materials[index] = material;
            }
            else
            {
                material.Id = materials.Any() ? materials.Max(m => m.Id) + 1 : 1;
                materials.Add(material);
            }

            material.LastModified = DateTime.Now;
            await _localStorage.SetItemAsync("materials", materials);
            _materials = materials;
            
            _notification.ShowSuccess("原料数据保存成功");
            return true;
        }
        catch (Exception ex)
        {
            _notification.ShowError($"保存原料数据失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> DeleteMaterialAsync(int id)
    {
        try
        {
            var materials = await GetMaterialsAsync();
            var material = materials.FirstOrDefault(m => m.Id == id);
            
            if (material != null)
            {
                materials.Remove(material);
                await _localStorage.SetItemAsync("materials", materials);
                _materials = materials;
                _notification.ShowSuccess("原料删除成功");
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            _notification.ShowError($"删除原料失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> SaveMaterialsAsync(List<MaterialModel> materials)
    {
        try
        {
            foreach (var material in materials)
            {
                material.LastModified = DateTime.Now;
            }
            
            await _localStorage.SetItemAsync("materials", materials);
            _materials = materials;
            
            _notification.ShowSuccess($"批量保存 {materials.Count} 条原料数据成功");
            return true;
        }
        catch (Exception ex)
        {
            _notification.ShowError($"批量保存失败: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> ImportMaterialsFromExcelAsync(Stream excelStream)
    {
        try
        {
            // TODO: 实现Excel导入逻辑
            _notification.ShowInfo("Excel导入功能开发中...");
            await Task.Delay(1000);
            return true;
        }
        catch (Exception ex)
        {
            _notification.ShowError($"Excel导入失败: {ex.Message}");
            return false;
        }
    }

    public async Task<byte[]> ExportMaterialsToExcelAsync()
    {
        try
        {
            // TODO: 实现Excel导出逻辑
            _notification.ShowInfo("Excel导出功能开发中...");
            await Task.Delay(1000);
            return Array.Empty<byte>();
        }
        catch (Exception ex)
        {
            _notification.ShowError($"Excel导出失败: {ex.Message}");
            return Array.Empty<byte>();
        }
    }

    public async Task<byte[]> GetExcelTemplateAsync()
    {
        try
        {
            // TODO: 实现Excel模板生成逻辑
            await Task.Delay(100);
            return Array.Empty<byte>();
        }
        catch (Exception ex)
        {
            _notification.ShowError($"获取Excel模板失败: {ex.Message}");
            return Array.Empty<byte>();
        }
    }

    private void InitializeDefaultMaterials()
    {
        _materials = new List<MaterialModel>
        {
            new() { Id = 1, MaterialCode = "M001", MaterialName = "碱性精粉", WetTFe = 63.76m, WetCaO = 1.94m, WetSiO2 = 4.95m, WetMgO = 1.85m, WetAl2O3 = 0.60m, Moisture = 8.20m, LossOnIgnition = 1.23m, Price = 752.21m, MinRatio = 0, MaxRatio = 30 },
            new() { Id = 2, MaterialCode = "M002", MaterialName = "酸性精粉", WetTFe = 64.89m, WetCaO = 0.70m, WetSiO2 = 6.32m, WetMgO = 0.92m, WetAl2O3 = 0.72m, Moisture = 9.90m, LossOnIgnition = -0.05m, Price = 752.21m, MinRatio = 0, MaxRatio = 30 },
            new() { Id = 3, MaterialCode = "M003", MaterialName = "海瑞", WetTFe = 58.07m, WetCaO = 0.10m, WetSiO2 = 6.21m, WetMgO = 0.28m, WetAl2O3 = 2.52m, Moisture = 6.00m, LossOnIgnition = 9.07m, Price = 822.98m, MinRatio = 0, MaxRatio = 25 },
            new() { Id = 4, MaterialCode = "M004", MaterialName = "印粉海娜", WetTFe = 63.66m, WetCaO = 0.10m, WetSiO2 = 4.01m, WetMgO = 0.24m, WetAl2O3 = 2.42m, Moisture = 6.70m, LossOnIgnition = 1.60m, Price = 832.98m, MinRatio = 15, MaxRatio = 25, IsSelected = true },
            new() { Id = 5, MaterialCode = "M005", MaterialName = "巴西粗粉", WetTFe = 64.64m, WetCaO = 0.20m, WetSiO2 = 4.69m, WetMgO = 0.11m, WetAl2O3 = 0.73m, Moisture = 6.70m, LossOnIgnition = 1.33m, Price = 1473.05m, MinRatio = 0, MaxRatio = 20 },
            new() { Id = 6, MaterialCode = "M006", MaterialName = "俄罗斯精粉", WetTFe = 62.95m, WetCaO = 1.71m, WetSiO2 = 4.61m, WetMgO = 3.70m, WetAl2O3 = 2.29m, Moisture = 10.00m, LossOnIgnition = -0.35m, Price = 772.21m, MinRatio = 15, MaxRatio = 25, IsSelected = true },
            new() { Id = 7, MaterialCode = "M007", MaterialName = "高炉返矿", WetTFe = 55.54m, WetCaO = 10.60m, WetSiO2 = 5.59m, WetMgO = 2.34m, WetAl2O3 = 2.09m, Moisture = 0.50m, LossOnIgnition = 1.73m, Price = 550.00m, MinRatio = 20, MaxRatio = 30, IsSelected = true },
            new() { Id = 8, MaterialCode = "M008", MaterialName = "回收料", WetTFe = 56.16m, WetCaO = 6.56m, WetSiO2 = 6.31m, WetMgO = 2.39m, WetAl2O3 = 2.51m, Moisture = 10.73m, LossOnIgnition = 1.74m, Price = 100.00m, MinRatio = 5, MaxRatio = 10, IsSelected = true },
            new() { Id = 9, MaterialCode = "M009", MaterialName = "钢渣", WetTFe = 26.46m, WetCaO = 28.15m, WetSiO2 = 15.43m, WetMgO = 2.79m, WetAl2O3 = 2.53m, Moisture = 7.60m, LossOnIgnition = 12.05m, Price = 550.00m, MinRatio = 2, MaxRatio = 6, IsSelected = true },
            new() { Id = 10, MaterialCode = "M010", MaterialName = "氧化铁皮", WetTFe = 69.73m, WetCaO = 0.50m, WetSiO2 = 1.50m, WetMgO = 0.00m, WetAl2O3 = 2.88m, Moisture = 5.90m, LossOnIgnition = -1.52m, Price = 750.00m, MinRatio = 0, MaxRatio = 8 },
            new() { Id = 11, MaterialCode = "M011", MaterialName = "生石灰", WetTFe = 0.00m, WetCaO = 71.74m, WetSiO2 = 3.52m, WetMgO = 2.28m, WetAl2O3 = 1.19m, Moisture = 7.00m, LossOnIgnition = 16.33m, Price = 219.00m, MinRatio = 5, MaxRatio = 8, IsSelected = true },
            new() { Id = 12, MaterialCode = "M012", MaterialName = "轻烧白云石", WetTFe = 0.00m, WetCaO = 42.67m, WetSiO2 = 5.31m, WetMgO = 26.12m, WetAl2O3 = 0.10m, Moisture = 1.50m, LossOnIgnition = 19.73m, Price = 183.76m, MinRatio = 2, MaxRatio = 5, IsSelected = true },
            new() { Id = 13, MaterialCode = "M013", MaterialName = "焦粉", WetTFe = 0.19m, WetCaO = 0.37m, WetSiO2 = 8.82m, WetMgO = 0.22m, WetAl2O3 = 3.31m, Moisture = 13.15m, LossOnIgnition = 79.40m, Price = 520.00m, MinRatio = 3, MaxRatio = 5, IsSelected = true },
            new() { Id = 14, MaterialCode = "M014", MaterialName = "澳粉纵横", WetTFe = 60.80m, WetCaO = 0.10m, WetSiO2 = 4.35m, WetMgO = 0.20m, WetAl2O3 = 2.30m, Moisture = 8.30m, LossOnIgnition = 6.89m, Price = 832.98m, MinRatio = 8, MaxRatio = 15, IsSelected = true }
        };
    }
}
