using System.ComponentModel.DataAnnotations;

namespace SinterBlendingSystem.Models;

/// <summary>
/// 原料数据模型
/// </summary>
public class MaterialModel
{
    public int Id { get; set; }
    
    [Required(ErrorMessage = "原料编号不能为空")]
    [StringLength(20, ErrorMessage = "原料编号长度不能超过20个字符")]
    public string MaterialCode { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "原料名称不能为空")]
    [StringLength(50, ErrorMessage = "原料名称长度不能超过50个字符")]
    public string MaterialName { get; set; } = string.Empty;
    
    [Range(0, 100, ErrorMessage = "湿基TFe含量必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F2}", ApplyFormatInEditMode = true)]
    public decimal WetTFe { get; set; }
    
    [Range(0, 100, ErrorMessage = "湿基CaO含量必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F2}", ApplyFormatInEditMode = true)]
    public decimal WetCaO { get; set; }
    
    [Range(0, 100, ErrorMessage = "湿基SiO2含量必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F2}", ApplyFormatInEditMode = true)]
    public decimal WetSiO2 { get; set; }
    
    [Range(0, 100, ErrorMessage = "湿基MgO含量必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F2}", ApplyFormatInEditMode = true)]
    public decimal WetMgO { get; set; }
    
    [Range(0, 100, ErrorMessage = "湿基Al2O3含量必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F2}", ApplyFormatInEditMode = true)]
    public decimal WetAl2O3 { get; set; }
    
    [Range(0, 20, ErrorMessage = "水分含量必须在0-20%之间")]
    [DisplayFormat(DataFormatString = "{0:F1}", ApplyFormatInEditMode = true)]
    public decimal Moisture { get; set; }
    
    [Range(-10, 100, ErrorMessage = "烧损率必须在-10-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F1}", ApplyFormatInEditMode = true)]
    public decimal LossOnIgnition { get; set; }
    
    [Range(0, 10000, ErrorMessage = "价格必须大于0")]
    [DisplayFormat(DataFormatString = "{0:F2}", ApplyFormatInEditMode = true)]
    public decimal Price { get; set; }
    
    [Range(0, 100, ErrorMessage = "配比下限必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F1}", ApplyFormatInEditMode = true)]
    public decimal MinRatio { get; set; }
    
    [Range(0, 100, ErrorMessage = "配比上限必须在0-100%之间")]
    [DisplayFormat(DataFormatString = "{0:F1}", ApplyFormatInEditMode = true)]
    public decimal MaxRatio { get; set; }
    
    public bool IsSelected { get; set; }
    
    public DateTime LastModified { get; set; } = DateTime.Now;
    
    // 计算属性：干基成分
    public decimal DryTFe => Moisture >= 100 ? 0 : WetTFe / (1 - Moisture / 100);
    public decimal DryCaO => Moisture >= 100 ? 0 : WetCaO / (1 - Moisture / 100);
    public decimal DrySiO2 => Moisture >= 100 ? 0 : WetSiO2 / (1 - Moisture / 100);
    public decimal DryMgO => Moisture >= 100 ? 0 : WetMgO / (1 - Moisture / 100);
    public decimal DryAl2O3 => Moisture >= 100 ? 0 : WetAl2O3 / (1 - Moisture / 100);
    
    // 验证方法
    public bool IsValid()
    {
        return MinRatio <= MaxRatio && Moisture < 100;
    }
    
    // 水分过高警告
    public bool HasMoistureWarning => Moisture > 20;
    
    // 配比范围错误
    public bool HasRatioError => MinRatio > MaxRatio;
}

/// <summary>
/// 优化目标模型
/// </summary>
public class OptimizationTarget
{
    [Range(50, 70, ErrorMessage = "TFe目标值必须在50-70%之间")]
    public decimal TFeTarget { get; set; } = 55.0m;
    
    [Range(1.0, 3.0, ErrorMessage = "碱度目标值必须在1.0-3.0之间")]
    public decimal RoTarget { get; set; } = 1.90m;
    
    [Range(1.0, 5.0, ErrorMessage = "MgO目标值必须在1.0-5.0%之间")]
    public decimal MgOTarget { get; set; } = 2.39m;
    
    [Range(1.0, 4.0, ErrorMessage = "Al2O3目标值必须在1.0-4.0%之间")]
    public decimal Al2O3Target { get; set; } = 1.89m;
}

/// <summary>
/// 约束范围模型
/// </summary>
public class ConstraintRanges
{
    public decimal TFeMin { get; set; } = 54m;
    public decimal TFeMax { get; set; } = 56m;
    public decimal RoMin { get; set; } = 1.88m;
    public decimal RoMax { get; set; } = 2.02m;
    public decimal MgOMin { get; set; } = 1.8m;
    public decimal MgOMax { get; set; } = 3.0m;
    public decimal Al2O3Min { get; set; } = 1.5m;
    public decimal Al2O3Max { get; set; } = 2.5m;
    public decimal CostMin { get; set; } = 600m;
    public decimal CostMax { get; set; } = 680m;
}

/// <summary>
/// 优化类型枚举
/// </summary>
public enum OptimizationType
{
    CostOptimal,
    QualityOptimal
}

/// <summary>
/// 优化结果模型
/// </summary>
public class OptimizationResult
{
    public bool Success { get; set; }
    public OptimizationType OptimizationType { get; set; }
    public Dictionary<string, decimal> OptimalRatios { get; set; } = new();
    public SinterProperties Properties { get; set; } = new();
    public decimal UnitCost { get; set; }
    public decimal ObjectiveValue { get; set; }
    public int Iterations { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public OptimizationResult? AlternativeSolution { get; set; }
}

/// <summary>
/// 烧结矿性质模型
/// </summary>
public class SinterProperties
{
    public decimal TFe { get; set; }
    public decimal R { get; set; }
    public decimal MgO { get; set; }
    public decimal Al2O3 { get; set; }
    public decimal Cost { get; set; }
}
